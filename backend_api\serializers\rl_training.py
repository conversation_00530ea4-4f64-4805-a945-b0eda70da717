from rest_framework import serializers
from backend_api.models.rl_training import RLTrainingTask, RLTrainingMetrics, RLResourceMetrics, RLTrainingConfig
from django.utils import timezone


class RLTrainingTaskSerializer(serializers.ModelSerializer):
    """强化学习训练任务序列化器"""
    
    # 添加计算字段
    duration = serializers.SerializerMethodField()
    progress = serializers.SerializerMethodField()
    
    class Meta:
        model = RLTrainingTask
        fields = '__all__'
        read_only_fields = ('training_id', 'created_time', 'updated_time', 'started_time', 'completed_time')
    
    def get_duration(self, obj):
        """计算训练持续时间（秒）"""
        if obj.started_time:
            end_time = obj.completed_time or timezone.now()
            return int((end_time - obj.started_time).total_seconds())
        return 0
    
    def get_progress(self, obj):
        """计算训练进度百分比"""
        if obj.status == 'completed':
            return 100
        elif obj.status == 'running' and obj.current_episode and obj.total_episodes:
            return min(100, int((obj.current_episode / obj.total_episodes) * 100))
        return 0


class RLTrainingStartSerializer(serializers.Serializer):
    """开始训练请求序列化器"""
    
    algorithm = serializers.DictField(required=True)
    simulation = serializers.DictField(required=True)
    agent = serializers.DictField(required=True)
    hyperParams = serializers.DictField(required=True)
    cluster = serializers.DictField(required=True)
    output = serializers.DictField(required=True)
    
    def validate_algorithm(self, value):
        required_fields = ['version', 'rlType']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"algorithm.{field} is required")
        
        # 验证RL类型
        valid_rl_types = ['PPO', 'SAC', 'TD3', 'DQN', 'A3C']
        if value.get('rlType') not in valid_rl_types:
            raise serializers.ValidationError(f"algorithm.rlType must be one of {valid_rl_types}")
        
        return value
    
    def validate_simulation(self, value):
        required_fields = ['dataSource', 'useExternalEnv']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"simulation.{field} is required")
        return value
    
    def validate_agent(self, value):
        required_fields = ['sampleTime', 'actionSpace', 'observationSpace', 'rewardFunction']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"agent.{field} is required")
        
        # 验证动作空间和观测空间
        if 'actionSpace' in value:
            action_space = value['actionSpace']
            if isinstance(action_space, dict):
                # 如果是字典格式，检查是否有type字段，如果没有则自动添加默认值
                if 'type' not in action_space:
                    # 根据其他字段推断类型
                    if 'low' in action_space and 'high' in action_space:
                        action_space['type'] = 'Box'
                    elif 'n' in action_space:
                        action_space['type'] = 'Discrete'
                    else:
                        action_space['type'] = 'Box'  # 默认为Box类型
                    value['actionSpace'] = action_space
            else:
                # 如果不是字典格式，转换为标准格式
                value['actionSpace'] = {
                    'type': 'Box',
                    'shape': action_space if isinstance(action_space, list) else [action_space]
                }
        
        # 验证观测空间
        if 'observationSpace' in value:
            obs_space = value['observationSpace']
            if isinstance(obs_space, dict):
                if 'type' not in obs_space:
                    if 'low' in obs_space and 'high' in obs_space:
                        obs_space['type'] = 'Box'
                    elif 'n' in obs_space:
                        obs_space['type'] = 'Discrete'
                    else:
                        obs_space['type'] = 'Box'
                    value['observationSpace'] = obs_space
            else:
                value['observationSpace'] = {
                    'type': 'Box',
                    'shape': obs_space if isinstance(obs_space, list) else [obs_space]
                }
        
        return value
    
    def validate_hyperParams(self, value):
        required_fields = ['learningRate', 'epochs', 'batchSize']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"hyperParams.{field} is required")
        
        # 验证参数范围
        if value.get('learningRate'):
            lr = float(value['learningRate'])
            if lr <= 0 or lr > 1:
                raise serializers.ValidationError("hyperParams.learningRate must be between 0 and 1")
        
        if value.get('epochs'):
            epochs = int(value['epochs'])
            if epochs <= 0:
                raise serializers.ValidationError("hyperParams.epochs must be positive")
        
        if value.get('batchSize'):
            batch_size = int(value['batchSize'])
            if batch_size <= 0:
                raise serializers.ValidationError("hyperParams.batchSize must be positive")
        
        return value
    
    def validate_cluster(self, value):
        required_fields = ['cpuCount', 'gpuCount']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"cluster.{field} is required")
        return value
    
    def validate_output(self, value):
        required_fields = ['savePath', 'saveName']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"output.{field} is required")
        return value


class RLTrainingMetricsSerializer(serializers.ModelSerializer):
    """强化学习训练指标序列化器"""
    
    class Meta:
        model = RLTrainingMetrics
        fields = '__all__'


class RLResourceMetricsSerializer(serializers.ModelSerializer):
    """强化学习资源指标序列化器"""
    
    class Meta:
        model = RLResourceMetrics
        fields = '__all__'


class RLTrainingConfigSerializer(serializers.ModelSerializer):
    """强化学习训练配置序列化器"""
    
    class Meta:
        model = RLTrainingConfig
        fields = '__all__'
        read_only_fields = ('config_id', 'created_time', 'updated_time')


class RLConfigSaveSerializer(serializers.Serializer):
    """保存配置请求序列化器"""
    
    configName = serializers.CharField(max_length=100)
    description = serializers.CharField(required=False, allow_blank=True)
    algorithm = serializers.DictField(required=True)
    simulation = serializers.DictField(required=True)
    agent = serializers.DictField(required=True)
    hyperParams = serializers.DictField(required=True)
    cluster = serializers.DictField(required=True)
    output = serializers.DictField(required=True)


class RLConfigImportSerializer(serializers.Serializer):
    """导入配置请求序列化器"""
    
    config = serializers.FileField(required=True)
    
    def validate_config(self, value):
        if not value.name.endswith('.json'):
            raise serializers.ValidationError("配置文件必须是JSON格式")
        
        # 验证文件大小（限制10MB）
        if value.size > 10 * 1024 * 1024:
            raise serializers.ValidationError("配置文件大小不能超过10MB")
        
        return value


class RLTrainingMetricsResponseSerializer(serializers.Serializer):
    """训练指标响应序列化器"""
    
    episodes = serializers.ListField(child=serializers.IntegerField())
    cumulativeRewards = serializers.ListField(child=serializers.FloatField())
    policyLosses = serializers.ListField(child=serializers.FloatField())
    
    # 添加更多训练指标
    averageRewards = serializers.ListField(child=serializers.FloatField(), required=False)
    episodeLengths = serializers.ListField(child=serializers.IntegerField(), required=False)
    explorationRates = serializers.ListField(child=serializers.FloatField(), required=False)


class RLResourceMetricsResponseSerializer(serializers.Serializer):
    """资源指标响应序列化器"""
    
    timestamps = serializers.ListField(child=serializers.DateTimeField())
    cpuUtilization = serializers.ListField(child=serializers.FloatField())
    npuUtilization = serializers.ListField(child=serializers.FloatField())
    memoryUtilization = serializers.ListField(child=serializers.FloatField())
    
    # 添加更多资源指标
    networkIO = serializers.ListField(child=serializers.FloatField(), required=False)
    diskIO = serializers.ListField(child=serializers.FloatField(), required=False)


class RLTrainingStatusSerializer(serializers.Serializer):
    """训练状态响应序列化器"""
    
    trainingId = serializers.CharField()
    status = serializers.CharField()
    currentEpisode = serializers.IntegerField()
    totalEpisodes = serializers.IntegerField()
    progress = serializers.FloatField()
    startedTime = serializers.DateTimeField()
    estimatedEndTime = serializers.DateTimeField(required=False)
    lastMetrics = serializers.DictField(required=False)


class RLTrainingListResponseSerializer(serializers.Serializer):
    """训练任务列表响应序列化器"""
    
    success = serializers.BooleanField()
    data = serializers.ListField(child=RLTrainingTaskSerializer())
    total = serializers.IntegerField()
    page = serializers.IntegerField()
    pageSize = serializers.IntegerField()


class RLConfigListResponseSerializer(serializers.Serializer):
    """配置列表响应序列化器"""
    
    success = serializers.BooleanField()
    data = serializers.ListField(child=RLTrainingConfigSerializer())
    total = serializers.IntegerField()
    page = serializers.IntegerField()
    pageSize = serializers.IntegerField() 