import uuid
from django.db import models


class Policy(models.Model):
    """
    策略管理模型
    策略是一个可以被导入的Python包。由于不同的仿真与场景下几乎不可能达成一个一致的策略接口规范，
    因此策略的规范实际上是通过评估代码隐式定义的：能够被评估代码正确导入运行的即为（实现该评估下规范的）策略。
    策略可以与一个算法模型关联，在策略代码中即可加载算法模型以与环境交互。
    """
    
    class StatusChoice(models.IntegerChoices):
        """状态choice"""
        PENDING = 0, '待入库测试'
        TESTING = 1, '测试中'
        ACTIVE = 2, '可用'
        FAILED = 3, '测试失败'
        DEPRECATED = 4, '已弃用'
    
    class PolicyType(models.IntegerChoices):
        """策略类型"""
        RULE_BASED = 0, '规则策略'
        RL_BASED = 1, '强化学习策略'
        HYBRID = 2, '混合策略'
    
    policy_id = models.UUIDField('策略ID', default=uuid.uuid4, editable=False)
    name = models.CharField('策略名称', max_length=255, unique=True)
    desc = models.CharField('策略描述', max_length=1024, null=True)
    
    # 策略代码
    code_file = models.FileField('策略代码', upload_to='policies/code', null=True)
    
    # 策略状态
    status = models.IntegerField('状态', choices=StatusChoice.choices, default=StatusChoice.PENDING)
    
    # 策略类型
    policy_type = models.IntegerField('策略类型', choices=PolicyType.choices, default=PolicyType.RULE_BASED)
    
    # 对于强化学习或混合策略，关联的算法模型
    model = models.ForeignKey('backend_api.Model', on_delete=models.SET_NULL, 
                             verbose_name='关联模型', db_constraint=False, null=True, blank=True)
    
    # 策略特征标签，存储为JSON格式
    features = models.JSONField('特征标签', null=True, blank=True)
    
    # 关联的评估，多对多关系
    evaluations = models.ManyToManyField('backend_api.Evaluation', 
                                       through='backend_api.PolicyEvaluation',
                                       related_name='policies', 
                                       verbose_name='关联评估')
    
    # 时间信息
    create_time = models.DateTimeField('创建时间', auto_now_add=True)
    update_time = models.DateTimeField('更新时间', auto_now=True)
    
    # 关联用户
    creater = models.ForeignKey('user.User', on_delete=models.CASCADE, 
                               verbose_name='创建用户', db_constraint=False, null=True)
    
    class Meta:
        verbose_name = '策略'
        verbose_name_plural = verbose_name
        ordering = ('-id',) 