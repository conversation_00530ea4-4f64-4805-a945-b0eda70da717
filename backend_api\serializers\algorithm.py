from rest_framework import serializers
from backend_api.models.algorithm import Algorithm
import uuid
import logging

logger = logging.getLogger(__name__)

class AlgorithmSerializer(serializers.ModelSerializer):
    algorithm_id = serializers.CharField(source='algorithm_id_str', read_only=True)
    
    class Meta:
        model = Algorithm
        fields = "__all__"
    
    def get_algorithm_id(self, obj):
        """
        自定义获取 algorithm_id 的方法
        """
        try:
            if not obj.algorithm_id:
                return None
            
            # 如果已经是 UUID 对象，直接转字符串
            if isinstance(obj.algorithm_id, uuid.UUID):
                return str(obj.algorithm_id)
            
            # 如果是字符串，尝试转换为 UUID 再转回字符串，以确保格式正确
            if isinstance(obj.algorithm_id, str):
                return str(uuid.UUID(obj.algorithm_id))
            
            return None
        except (ValueError, AttributeError, TypeError) as e:
            logger.error(f"Error converting algorithm_id for algorithm {obj.id}: {str(e)}")
            # 如果转换失败，生成新的 UUID
            new_uuid = uuid.uuid4()
            try:
                obj.algorithm_id = new_uuid
                obj.save(update_fields=['algorithm_id'])
                logger.info(f"Generated new UUID {new_uuid} for algorithm {obj.id}")
                return str(new_uuid)
            except Exception as save_error:
                logger.error(f"Error saving new UUID for algorithm {obj.id}: {str(save_error)}")
                return None


