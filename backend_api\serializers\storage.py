from rest_framework import serializers


class StorageListSerializer(serializers.Serializer):
    
    url = serializers.CharField(max_length=255)
    files = serializers.ListField(child=serializers.CharField(max_length=255))


class StorageCreateDirSerializer(serializers.Serializer):
    
    url = serializers.CharField(max_length=255)
    
    dir = serializers.CharField(max_length=255)


class StorageUploadSerializer(serializers.Serializer):
    
    url = serializers.CharField(max_length=255)
    
    files = serializers.ListField(child=serializers.FileField())
    
    
class StorageDownloadSerializer(serializers.Serializer):
    
    url = serializers.CharField(max_length=255)
    
    
class StorageDeleteSerializer(serializers.Serializer):
    
    url = serializers.CharField(max_length=255)
        
