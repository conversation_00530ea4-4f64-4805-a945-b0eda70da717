import os
from rest_framework import status
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework.decorators import action
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from dotenv import load_dotenv

from backend_api.models.environment import Environment
from backend_api.serializers.environment import (
    EnvironmentSerializer,
    EnvironmentDetailSerializer,
    EnvironmentCreateSerializer
)

load_dotenv()

GITLAB_URL = os.getenv("GITLAB_URL")
HARBOR_URL = os.getenv("HARBOR_URL")

class EnvironmentViewSet(ModelViewSet):
    """
    环境管理视图集
    支持环境的增删改查操作
    """
    queryset = Environment.objects.all()
    serializer_class = EnvironmentSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['status']

    def get_serializer_class(self):
        """根据不同的操作选择不同的序列化器"""
        if self.action == 'create':
            return EnvironmentCreateSerializer
        elif self.action in ['retrieve', 'list']:
            return EnvironmentDetailSerializer
        return EnvironmentSerializer

    def get_queryset(self):
        """获取查询集，支持按名称搜索"""
        user = self.request.user
        queryset = Environment.objects.all()

        # 管理员可以看到所有环境，普通用户只能看到自己创建的
        if not user.is_staff:
            queryset = queryset.filter(creator=user.id)

        # 支持按名称搜索
        name = self.request.query_params.get('name', '')
        if name:
            queryset = queryset.filter(name__icontains=name)

        return queryset

    def create(self, request, *args, **kwargs):
        """创建环境"""
        # 添加创建者信息
        request.data['creator'] = request.user.id
        # 添加 GitLab 和 Harbor URL
        if not request.data.get('gitlab_url'):
            request.data['gitlab_url'] = f"{GITLAB_URL}/{request.user.username}/{request.data['artifact_name']}"
        if not request.data.get('harbor_url'):
            request.data['harbor_url'] = f"{HARBOR_URL}/{request.user.username}/{request.data['artifact_name']}:{request.data['version']}"
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            
            self.perform_create(serializer)
            return Response(
                {
                    'code': 200,
                    'msg': '创建环境成功',
                    'data': serializer.data
                },
                status=status.HTTP_201_CREATED
            )
        return Response(
            {
                'code': 400,
                'msg': '创建环境失败',
                'err_msg': serializer.errors
            },
            status=status.HTTP_400_BAD_REQUEST
        )

    def update(self, request, *args, **kwargs):
        """更新环境"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        
        # 只允许更新未弃用的环境
        if instance.status == Environment.StatusChoice.DEPRECATED:
            return Response(
                {
                    'code': 400,
                    'msg': '更新环境失败',
                    'err_msg': '已弃用的环境不能修改'
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            return Response(
                {
                    'code': 200,
                    'msg': '更新环境成功',
                    'data': serializer.data
                }
            )
        return Response(
            {
                'code': 400,
                'msg': '更新环境失败',
                'err_msg': serializer.errors
            },
            status=status.HTTP_400_BAD_REQUEST
        )

    def destroy(self, request, *args, **kwargs):
        """删除环境（标记为已弃用）"""
        instance = self.get_object()
        
        # 已经是弃用状态的不能重复弃用
        if instance.status == Environment.StatusChoice.DEPRECATED:
            return Response(
                {
                    'code': 400,
                    'msg': '删除环境失败',
                    'err_msg': '环境已经是弃用状态'
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        # 标记为已弃用
        instance.status = Environment.StatusChoice.DEPRECATED
        instance.deprecated_time = timezone.now()
        instance.save()

        return Response(
            {
                'code': 200,
                'msg': '删除环境成功'
            },
            status=status.HTTP_200_OK
        )

    @action(detail=True, methods=['post'])
    def deprecate(self, request, pk=None):
        """弃用环境"""
        instance = self.get_object()
        
        if instance.status == Environment.StatusChoice.DEPRECATED:
            return Response(
                {
                    'code': 400,
                    'msg': '弃用环境失败',
                    'err_msg': '环境已经是弃用状态'
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        instance.status = Environment.StatusChoice.DEPRECATED
        instance.deprecated_time = timezone.now()
        instance.save()

        serializer = self.get_serializer(instance)
        return Response(
            {
                'code': 200,
                'msg': '弃用环境成功',
                'data': serializer.data
            }
        )

    def list(self, request, *args, **kwargs):
        """获取环境列表"""
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(
                serializer.data
            )

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def retrieve(self, request, *args, **kwargs):
        """获取环境详情"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data) 