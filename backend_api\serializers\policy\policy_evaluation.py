from rest_framework import serializers
from backend_api.models.policy.policy_evaluation import PolicyEvaluation


class PolicyEvaluationSerializer(serializers.ModelSerializer):
    """
    策略评估序列化器
    """
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    policy_name = serializers.CharField(source='policy.name', read_only=True)
    evaluation_name = serializers.CharField(source='evaluation.name', read_only=True)
    
    class Meta:
        model = PolicyEvaluation
        fields = [
            'id', 'evaluation_id', 'policy', 'policy_name',
            'evaluation', 'evaluation_name', 'status', 'status_display',
            'score', 'result', 'log', 'elo_rating',
            'create_time', 'start_time', 'end_time', 'is_applied'
        ] 