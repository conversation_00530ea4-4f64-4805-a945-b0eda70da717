# 训练模型管理API文档

## 📋 概述

训练模型管理API提供了完整的模型生命周期管理功能，包括模型信息记录、性能指标管理、模型导出和推理等功能。

## 🔗 接口列表

### 1. 模型列表

**接口路径**: `/backend/training/models`  
**请求方法**: `GET`  
**权限要求**: 需要登录认证

#### 查询参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `task_id` | int | 否 | 按任务ID过滤 |
| `export_status` | string | 否 | 按导出状态过滤 (pending/exporting/completed/failed) |
| `architecture` | string | 否 | 按模型架构过滤 |
| `min_accuracy` | float | 否 | 最小准确率 |
| `max_model_size` | float | 否 | 最大模型大小(MB) |
| `search` | string | 否 | 搜索关键词 |
| `page` | int | 否 | 页码 (默认: 1) |
| `page_size` | int | 否 | 每页数量 (默认: 20) |

#### 调用示例

```javascript
// 获取所有模型
fetch('/backend/training/models', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('模型列表:', data.data);
});

// 按任务ID过滤
fetch('/backend/training/models?task_id=123&page=1&page_size=10', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('任务123的模型:', data.data);
});
```

### 2. 创建模型记录

**接口路径**: `/backend/training/models/create`  
**请求方法**: `POST`  
**权限要求**: 需要登录认证

#### 请求参数

```json
{
    "task_id": 123,                    // 可选，训练任务ID
    "model_path": "/path/to/model.pt", // 可选，模型文件路径
    "accuracy": 0.85,                  // 可选，准确率
    "precision": 0.82,                 // 可选，精度
    "recall": 0.78,                    // 可选，召回率
    "inference_speed": 45.2,           // 可选，推理速度(FPS)
    "inference_time_ms": 22.1,         // 可选，推理时间(毫秒)
    "model_size_mb": 14.5,             // 可选，模型大小(MB)
    "num_classes": 80,                 // 可选，类别数量
    "architecture": "YOLOv8n",         // 可选，模型架构
    "fitness": 0.81,                   // 可选，适应度分数
    "notes": "训练备注信息"             // 可选，备注信息
}
```

**注意**: 所有字段都是可选的，可以为空值。如果不提供 `task_id` 或 `model_path`，系统会自动生成模型名称。

#### 调用示例

```javascript
const modelData = {
    task_id: 123,
    model_path: '/runs/detect/train/weights/best.pt',
    accuracy: 0.85,
    precision: 0.82,
    recall: 0.78,
    inference_speed: 45.2,
    inference_time_ms: 22.1,
    model_size_mb: 14.5,
    num_classes: 80,
    architecture: 'YOLOv8n',
    fitness: 0.81
};

fetch('/backend/training/models/create', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer your_token',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(modelData)
})
.then(response => response.json())
.then(data => {
    console.log('模型创建成功:', data.data);
});
```

### 3. 模型详情

**接口路径**: `/backend/training/models/{model_id}`  
**请求方法**: `GET/PUT/DELETE`  
**权限要求**: 需要登录认证

#### GET - 获取模型详情

```javascript
fetch('/backend/training/models/123', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('模型详情:', data.data);
});
```

#### PUT - 更新模型信息

```javascript
const updateData = {
    accuracy: 0.87,
    notes: '更新后的备注'
};

fetch('/backend/training/models/123', {
    method: 'PUT',
    headers: {
        'Authorization': 'Bearer your_token',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(updateData)
})
.then(response => response.json())
.then(data => {
    console.log('模型更新成功:', data.data);
});
```

### 4. 模型转换 (.pt转.om)

**接口路径**: `/backend/training/models/convert`  
**请求方法**: `POST`  
**权限要求**: 需要登录认证

#### 请求参数

```json
{
    "model_id": 123,                 // 必填，模型ID
    "conversion_format": "om",       // 可选，转换格式，默认为"om"
    "chip_name": "910B3"             // 可选，芯片型号，默认为"910B3"
}
```

#### 调用示例

```javascript
const convertData = {
    model_id: 123,
    conversion_format: 'om',
    chip_name: '910B3'
};

fetch('/backend/training/models/convert', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer your_token',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(convertData)
})
.then(response => response.json())
.then(data => {
    console.log('转换任务已启动:', data.data);
});
```

### 5. 模型转换状态查询

**接口路径**: `/backend/training/models/convert/{conversion_log_id}/status`
**请求方法**: `GET`
**权限要求**: 需要登录认证

#### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `conversion_log_id` | int | 是 | 转换日志ID |

#### 调用示例

```javascript
// 查询转换状态
const conversionLogId = 123;
fetch(`/backend/training/models/convert/${conversionLogId}/status`, {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('转换状态:', data);

    // 根据状态进行不同处理
    if (data.status === 'completed') {
        console.log('转换完成:', data.data.output_path);
    } else if (data.status === 'failed') {
        console.log('转换失败:', data.data.error_message);
    } else if (data.status === 'processing') {
        console.log('转换中，继续轮询...');
        // 可以设置定时器继续轮询
        setTimeout(() => checkConversionStatus(conversionLogId), 3000);
    }
});
```

#### 响应示例

**转换中:**
```json
{
    "success": true,
    "message": "转换任务正在处理中",
    "data": {
        "conversion_log_id": 123,
        "model_id": 456,
        "model_name": "task_1_best",
        "conversion_format": "om",
        "conversion_device": "npu",
        "status": "processing",
        "start_time": "2024-01-15 10:30:00",
        "end_time": null,
        "output_path": null,
        "file_size_mb": null,
        "error_message": null,
        "log_content": null
    },
    "status": "processing"
}
```

**转换完成:**
```json
{
    "success": true,
    "message": "转换任务已完成",
    "data": {
        "conversion_log_id": 123,
        "model_id": 456,
        "model_name": "task_1_best",
        "conversion_format": "om",
        "conversion_device": "npu",
        "status": "completed",
        "start_time": "2024-01-15 10:30:00",
        "end_time": "2024-01-15 10:35:00",
        "output_path": "/workspace/models/task_1_best.om",
        "file_size_mb": 25.6,
        "error_message": null,
        "log_content": "转换成功日志..."
    },
    "status": "completed"
}
```

**转换失败:**
```json
{
    "success": false,
    "message": "转换任务失败",
    "data": {
        "conversion_log_id": 123,
        "model_id": 456,
        "model_name": "task_1_best",
        "conversion_format": "om",
        "conversion_device": "npu",
        "status": "failed",
        "start_time": "2024-01-15 10:30:00",
        "end_time": "2024-01-15 10:32:00",
        "output_path": null,
        "file_size_mb": null,
        "error_message": "模型转换失败：不支持的模型格式",
        "log_content": "错误日志..."
    },
    "status": "failed"
}
```

### 6. 模型推理

**接口路径**: `/backend/training/models/inference`
**请求方法**: `POST`
**权限要求**: 需要登录认证

#### 请求参数

```json
{
    "model_id": 123,                  // 必填，模型ID
    "input_source": "/path/to/image.jpg",  // 必填，输入源路径
    "confidence_threshold": 0.5,      // 可选，置信度阈值，默认0.5
    "iou_threshold": 0.45,            // 可选，IOU阈值，默认0.45
    "max_detections": 1000,           // 可选，最大检测数量，默认1000
    "save_result": true,              // 可选，是否保存结果，默认true
    "return_image": false             // 可选，是否返回图像数据，默认true
}
```

#### 调用示例

```javascript
const inferenceData = {
    model_id: 123,
    input_source: '/path/to/test_image.jpg',
    confidence_threshold: 0.5,
    iou_threshold: 0.45,
    max_detections: 1000,
    save_result: true,
    return_image: true
};

fetch('/backend/training/models/inference', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer your_token',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(inferenceData)
})
.then(response => response.json())
.then(data => {
    console.log('推理结果:', data.data.result);
});
```

### 6. 导出日志

**接口路径**: `/backend/training/models/{model_id}/export-logs`  
**请求方法**: `GET`  
**权限要求**: 需要登录认证

```javascript
// 获取特定模型的导出日志
fetch('/backend/training/models/123/export-logs', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('导出日志:', data.data);
});

// 获取所有导出日志
fetch('/backend/training/export-logs', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('所有导出日志:', data.data);
});
```

### 7. 推理日志

**接口路径**: `/backend/training/models/{model_id}/inference-logs`  
**请求方法**: `GET`  
**权限要求**: 需要登录认证

```javascript
// 获取特定模型的推理日志
fetch('/backend/training/models/123/inference-logs', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('推理日志:', data.data);
});
```

## 📊 数据模型

### TrainingModel 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `id` | int | 是 | 模型ID (自动生成) |
| `task_id` | int | 否 | 关联的训练任务ID (可为空) |
| `model_name` | string | 否 | 模型名称 (任务ID+文件名，可为空) |
| `model_path` | string | 否 | .pt文件路径 (可为空) |
| `accuracy` | float | 否 | 准确率 (mAP50，可为空) |
| `precision` | float | 否 | 精度 (可为空) |
| `recall` | float | 否 | 召回率 (可为空) |
| `inference_speed` | float | 否 | 推理速度 (FPS，可为空) |
| `inference_time_ms` | float | 否 | 推理时间 (毫秒，可为空) |
| `model_size_mb` | float | 否 | 模型大小 (MB，可为空) |
| `export_status` | string | 否 | 导出状态 (默认: pending) |
| `om_model_path` | string | 否 | OM模型路径 (可为空) |
| `num_classes` | int | 否 | 类别数量 (可为空) |
| `architecture` | string | 否 | 模型架构 (可为空) |
| `fitness` | float | 否 | 适应度分数 (可为空) |
| `created_by` | int | 否 | 创建者ID (可为空) |
| `created_time` | datetime | 否 | 创建时间 (可为空) |
| `updated_time` | datetime | 否 | 更新时间 (可为空) |
| `notes` | string | 否 | 备注信息 (可为空) |
| `validation_error` | string | 否 | 验证错误信息 (可为空) |

## 🔄 前端轮询示例

### 模型转换状态轮询

```javascript
// 启动模型转换并轮询状态
async function convertModelAndPoll(modelId) {
    try {
        // 1. 启动转换任务
        const convertResponse = await fetch('/backend/training/models/convert', {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer your_token',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model_id: modelId,
                conversion_format: 'om',
                chip_name: '910B3'
            })
        });

        const convertData = await convertResponse.json();

        if (!convertData.success) {
            throw new Error(convertData.message);
        }

        const conversionLogId = convertData.data.conversion_log_id;
        console.log('转换任务已启动，日志ID:', conversionLogId);

        // 2. 开始轮询状态
        return await pollConversionStatus(conversionLogId);

    } catch (error) {
        console.error('转换失败:', error);
        throw error;
    }
}

// 轮询转换状态
async function pollConversionStatus(conversionLogId, maxAttempts = 60, interval = 5000) {
    let attempts = 0;

    while (attempts < maxAttempts) {
        try {
            const response = await fetch(`/backend/training/models/convert/${conversionLogId}/status`, {
                headers: { 'Authorization': 'Bearer your_token' }
            });

            const data = await response.json();

            console.log(`轮询第${attempts + 1}次，状态: ${data.status}`);

            if (data.status === 'completed') {
                console.log('✅ 转换完成!', data.data);
                return data.data;
            } else if (data.status === 'failed') {
                console.error('❌ 转换失败:', data.data.error_message);
                throw new Error(data.data.error_message);
            }

            // 如果还在处理中，等待后继续轮询
            if (data.status === 'processing' || data.status === 'started') {
                await new Promise(resolve => setTimeout(resolve, interval));
                attempts++;
            } else {
                throw new Error(`未知状态: ${data.status}`);
            }

        } catch (error) {
            console.error('轮询出错:', error);
            attempts++;
            if (attempts >= maxAttempts) {
                throw new Error('轮询超时或失败');
            }
            await new Promise(resolve => setTimeout(resolve, interval));
        }
    }

    throw new Error('轮询超时');
}

// 使用示例
convertModelAndPoll(123)
    .then(result => {
        console.log('转换成功:', result.output_path);
        // 可以继续进行推理等操作
    })
    .catch(error => {
        console.error('转换过程失败:', error);
    });
```

## 🎯 使用流程

1. **训练完成后创建模型记录**
2. **查看模型列表和详情**
3. **导出模型为OM格式**
4. **使用模型进行推理**
5. **查看导出和推理日志**

## ⚠️ 注意事项

1. **模型路径**: 必须是有效的.pt文件路径
2. **权限控制**: 用户只能操作自己创建的模型
3. **导出状态**: 导出中的模型不能重复导出
4. **文件管理**: 删除模型记录不会删除实际文件
5. **日志记录**: 所有操作都会记录详细日志

这些接口提供了完整的训练模型管理功能，支持模型的全生命周期管理！
