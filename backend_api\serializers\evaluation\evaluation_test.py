from rest_framework import serializers
from backend_api.models.evaluation.evaluation_test import EvaluationTest


class EvaluationTestSerializer(serializers.ModelSerializer):
    """
    评估测试序列化器
    """
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = EvaluationTest
        fields = [
            'id', 'test_id', 'name', 'desc', 'test_code', 
            'status', 'status_display', 'result', 'log', 'evaluation',
            'create_time', 'start_time', 'end_time', 'is_default'
        ] 