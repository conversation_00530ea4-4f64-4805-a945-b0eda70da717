from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework.parsers import MultiPartParser
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_204_NO_CONTENT, HTTP_400_BAD_REQUEST

from backend_api.models.simulator import Simulator
from backend_api.serializers.simulator import SimulatorSerializer


class SimulatorViewSets(ModelViewSet):
    """
    声明仿真器资源类 用户操作:获取仿真信息  更新仿真  删除仿真 创建仿真
    """
    queryset = Simulator.objects.all()
    serializer_class = SimulatorSerializer
    parser_classes = [MultiPartParser]
    
    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            name =  self.request.query_params.get('name', '')
            if name and name != '':
                queryset = Simulator.objects.filter(name__icontains=name).all()
            else:
                queryset = Simulator.objects.all()
        else:
            name =  self.request.query_params.get('name', '')
            if name and name != '':
                queryset = Simulator.objects.filter(name__icontains=name).all()
            else:
                # 根据 owner 过滤查询集
                queryset = Simulator.objects.all()

        return queryset
    
    def create(self, request):
        user = request.user
        query_data = request.data.copy()
        query_data["creater"] = user.id
        name = request.data["name"]
        if Simulator.objects.filter(name=name).count() > 0:
            return Response({"err_msg": f"Simulator {name} 已存在", "msg": "对象已存在错误", "code": "object_exists"}, status=HTTP_400_BAD_REQUEST)
        
        seria = self.get_serializer(data=query_data)
        if seria.is_valid():
            scen_inst = seria.save()
            return Response({"data": self.get_serializer(scen_inst).data, "msg": "create scenario success.", "code": 200}, status=HTTP_201_CREATED)
        else:
            return Response({"err_msg": seria.errors, "msg": "参数错误", "code": 401}, status=HTTP_400_BAD_REQUEST)
    
