import uuid
from django.db import models


class Task(models.Model):
    
    class StatusChoice(models.IntegerChoices):
        """状态choice"""
        PENDING = 0, '队列中'
        INITIALIZED = 1, '初始化'
        RUNNING = 2, '运行'
        PAUSED = 3, '暂停'
        TERMINATED = 4, '终止'
        EXHAUSTED = 5, '耗尽'
        SUCCEEDED = 6, '成功'
        FAILED = 7, '失败'
        EXCEPTIONAL = 8, '异常'
        COMPLETED = 9, '完成'
        RESUMED = 10, '恢复'
        PAUSING = 11, '暂停中...'
        RESUMING = 12, '恢复中...'
        TERMINATING = 13, '终止中...'

    class TaskType(models.TextChoices):
        """任务类型"""
        TRAINING = 'training', '训练'
        EVALUATION = 'evaluation', '评估'
        DEDUCTION = 'deduction', '推演'
        
    class AlgoType(models.TextChoices):
        """算法类型"""
        BIGMODEL = 'bigmodel', '大模型'
        REINFORCELEARNING='reinforcelearning', '强化学习'
        DEEPLEARNING='deeplearning', '深度学习'
        
    task_id = models.UUIDField('任务id', default=uuid.uuid4, editable=False)
    name = models.CharField('任务名称', max_length=255, blank=False)
    task_type = models.CharField(
        '任务类型',
        max_length=20,
        choices=TaskType.choices,
        default=TaskType.TRAINING
    )
    
    # 时间信息
    create_time = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    start_time = models.DateTimeField('开始时间', null=True)
    end_time = models.DateTimeField('释放时间', null=True)
    status = models.SmallIntegerField('状态', null=True, choices=StatusChoice.choices, default=StatusChoice.PENDING)
    
    # 删除标记
    is_deleted = models.BooleanField('是否删除', default=False)
    deleted_time = models.DateTimeField('删除时间', null=True)
    
    # 用户信息
    owner = models.ForeignKey('user.User', on_delete=models.CASCADE, verbose_name='创建用户', db_constraint=False, null=True)
    
    class Meta:
        verbose_name = '任务实例'
        verbose_name_plural = verbose_name
        ordering = ('-id',)

