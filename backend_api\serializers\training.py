from rest_framework import serializers
from backend_api.models.training import TrainingTask, DLTrainingConfig

class AlgorithmConfigSerializer(serializers.Serializer):
    version = serializers.CharField(required=False, default='v8', allow_blank=True)
    modelPath = serializers.CharField(required=True)

class DatasetConfigSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)  # YOLOv8必需
    name = serializers.CharField(required=True)  # YOLOv8必需

class TrainingConfigSerializer(serializers.Serializer):
    dataset = DatasetConfigSerializer(required=True)  # YOLOv8必需
    validationRatio = serializers.FloatField(required=False, default=0.2)

class ResourceConfigSerializer(serializers.Serializer):
    cpuCount = serializers.CharField(required=False, default='0', allow_blank=True)
    npuCount = serializers.Char<PERSON>ield(required=False, default='0', allow_blank=True)
    storageSize = serializers.Char<PERSON>ield(required=False, default='0', allow_blank=True)

class ParametersConfigSerializer(serializers.Serializer):
    learningRate = serializers.CharField(required=False, default='0.01', allow_blank=True)
    epochs = serializers.CharField(required=False, default='100', allow_blank=True)
    batchSize = serializers.CharField(required=False, default='16', allow_blank=True)
    maxGradNorm = serializers.CharField(required=False, default='0', allow_blank=True)
    maxSamples = serializers.CharField(required=False, default='0', allow_blank=True)
    gradAccumulation = serializers.CharField(required=False, default='1', allow_blank=True)
    learningRateStrategy = serializers.CharField(required=False, default='', allow_blank=True)
    computeType = serializers.CharField(required=False, default='float32', allow_blank=True)

class OtherParamsConfigSerializer(serializers.Serializer):
    optimizer = serializers.CharField(required=False, default='SGD', allow_blank=True)
    momentum = serializers.CharField(required=False, default='0.937', allow_blank=True)
    weightDecay = serializers.CharField(required=False, default='0.0005', allow_blank=True)
    epsilon = serializers.CharField(required=False, default='1e-8', allow_blank=True)
    dropout = serializers.CharField(required=False, default='0', allow_blank=True)
    labelSmoothing = serializers.CharField(required=False, default='0', allow_blank=True)
    useGradientClipping = serializers.BooleanField(required=False, default=False)
    useMixedPrecision = serializers.BooleanField(required=False, default=False)
    earlyStopping = serializers.IntegerField(required=False, default=0)
    checkpointFreq = serializers.IntegerField(required=False, default=0)
    warmupSteps = serializers.IntegerField(required=False, default=0)
    logFreq = serializers.IntegerField(required=False, default=100)
    activation = serializers.CharField(required=False, default='', allow_blank=True)
    initialization = serializers.CharField(required=False, default='', allow_blank=True)
    normalization = serializers.CharField(required=False, default='', allow_blank=True)
    attentionHeads = serializers.IntegerField(required=False, default=0)

class TrainingRequestSerializer(serializers.Serializer):
    algorithm = AlgorithmConfigSerializer(required=True)  # 只有这个是必需的
    training = TrainingConfigSerializer(required=True)    # 只有这个是必需的
    resources = ResourceConfigSerializer(required=False)  # 可选
    parameters = ParametersConfigSerializer(required=False)  # 可选
    otherParams = OtherParamsConfigSerializer(required=False)  # 可选

    def create(self, validated_data):
        # 确保使用默认值，处理空白字符串
        parameters = validated_data.get('parameters', {})
        other_params = validated_data.get('otherParams', {})
        resources = validated_data.get('resources', {})
        
        # 处理空白字符串，如果为空白则使用默认值
        def get_value_or_default(data, key, default):
            value = data.get(key, default)
            return default if value == '' else value
        
        return TrainingTask.objects.create(
            # 算法配置
            algorithm_version=get_value_or_default(validated_data['algorithm'], 'version', 'v8'),
            model_path=validated_data['algorithm']['modelPath'],
            
            # 训练数据集配置
            dataset_id=validated_data['training']['dataset']['id'],
            dataset_name=validated_data['training']['dataset']['name'],
            validation_ratio=validated_data['training'].get('validationRatio', 0.2),
            
            # 资源配置
            cpu_count=get_value_or_default(resources, 'cpuCount', '0'),
            npu_count=get_value_or_default(resources, 'npuCount', '0'),
            storage_size=get_value_or_default(resources, 'storageSize', '0'),
            
            # 训练参数
            learning_rate=get_value_or_default(parameters, 'learningRate', '0.01'),
            epochs=get_value_or_default(parameters, 'epochs', '100'),
            max_grad_norm=get_value_or_default(parameters, 'maxGradNorm', '0'),
            max_samples=get_value_or_default(parameters, 'maxSamples', '0'),
            grad_accumulation=get_value_or_default(parameters, 'gradAccumulation', '1'),
            batch_size=get_value_or_default(parameters, 'batchSize', '16'),
            learning_rate_strategy=get_value_or_default(parameters, 'learningRateStrategy', ''),
            compute_type=get_value_or_default(parameters, 'computeType', 'float32'),
            
            # 其他参数
            optimizer=get_value_or_default(other_params, 'optimizer', 'SGD'),
            momentum=get_value_or_default(other_params, 'momentum', '0.937'),
            weight_decay=get_value_or_default(other_params, 'weightDecay', '0.0005'),
            epsilon=get_value_or_default(other_params, 'epsilon', '1e-8'),
            dropout=get_value_or_default(other_params, 'dropout', '0'),
            label_smoothing=get_value_or_default(other_params, 'labelSmoothing', '0'),
            use_gradient_clipping=other_params.get('useGradientClipping', False),
            use_mixed_precision=other_params.get('useMixedPrecision', False),
            early_stopping=other_params.get('earlyStopping', 0),
            checkpoint_freq=other_params.get('checkpointFreq', 0),
            warmup_steps=other_params.get('warmupSteps', 0),
            log_freq=other_params.get('logFreq', 100),
            activation=get_value_or_default(other_params, 'activation', ''),
            initialization=get_value_or_default(other_params, 'initialization', ''),
            normalization=get_value_or_default(other_params, 'normalization', ''),
            attention_heads=other_params.get('attentionHeads', 0)
        )




# 深度学习训练配置管理序列化器
class DLTrainingConfigSerializer(serializers.ModelSerializer):
    """深度学习训练配置序列化器"""
    
    class Meta:
        model = DLTrainingConfig
        fields = '__all__'
        read_only_fields = ('config_id', 'created_time', 'updated_time')


class DLConfigSaveSerializer(serializers.Serializer):
    """保存深度学习配置请求序列化器"""
    
    configName = serializers.CharField(max_length=100)
    description = serializers.CharField(required=False, allow_blank=True)
    algorithm = serializers.DictField(required=True)
    training = serializers.DictField(required=True)
    resources = serializers.DictField(required=False)
    parameters = serializers.DictField(required=False)
    otherParams = serializers.DictField(required=False)


class DLConfigImportSerializer(serializers.Serializer):
    """导入深度学习配置请求序列化器"""
    
    config = serializers.FileField(required=True)
    
    def validate_config(self, value):
        if not value.name.endswith('.json'):
            raise serializers.ValidationError("配置文件必须是JSON格式")
        
        # 验证文件大小（限制10MB）
        if value.size > 10 * 1024 * 1024:
            raise serializers.ValidationError("配置文件大小不能超过10MB")
        
        return value


class DLConfigListResponseSerializer(serializers.Serializer):
    """深度学习配置列表响应序列化器"""
    
    success = serializers.BooleanField()
    data = serializers.ListField(child=DLTrainingConfigSerializer())
    total = serializers.IntegerField()
    page = serializers.IntegerField()
    pageSize = serializers.IntegerField()
    totalPages = serializers.IntegerField()
