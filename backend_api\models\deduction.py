import uuid
from django.db import models


class Deduction(models.Model):
    
    class StatusChoice(models.IntegerChoices):
        """状态choice"""
        PENDING = 0, '队列中'
        INITIALIZED = 1, '初始化'
        RUNNING = 2, '生成中'
        SUCCEEDED = 3, '成功'
        FAILED = 4, '失败'
        
    task = models.OneToOneField(
        'backend_api.Task',
        on_delete=models.CASCADE,
        related_name='deduction',
        verbose_name='关联任务',
        null=True
    )
    
    deduction_id = models.UUIDField('推演id', default=uuid.uuid4, editable=False)
    name = models.CharField('推演名称', max_length=255, unique=False)
    desc = models.CharField('推演描述', max_length=1024, null=True)
    file = models.CharField('推演存储地址', max_length=255, null=True)
    ts = models.CharField('时间戳', max_length=255, null=True)
    
    status = models.IntegerField('推演状态', choices=StatusChoice.choices, default=StatusChoice.PENDING, null=True)
    
    create_time = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    start_time = models.DateTimeField('开始时间', null=True)
    end_time = models.DateTimeField('结束时间', null=True)
    
    creater = models.ForeignKey('user.User', on_delete=models.CASCADE, verbose_name='创建用户', db_constraint=False, null=True)
    model = models.ForeignKey('backend_api.Model', on_delete=models.CASCADE, verbose_name='关联的model', db_constraint=False, null=True)
    
    class Meta:
        verbose_name = '推演实例'
        verbose_name_plural = verbose_name
        ordering = ('-id',)