import logging
import sys
from channels.generic.websocket import AsyncWebsocketConsumer
import json
from django.http import QueryDict
from utils import k8s_client


logger = logging.getLogger(__name__)

class TerminalConsumer(AsyncWebsocketConsumer):
    
    async def connect(self):
        query_string = self.scope.get('query_string')
        log_args = QueryDict(query_string=query_string, encoding='utf-8')
        self.container_name = log_args.get('container_name')
        self.pod_name = log_args.get('pod_name')
        
        self.room_group_name = 'chat' + self.pod_name
        print(f'connect to group {self.room_group_name}  channel name: {self.channel_name}', file=sys.stderr)
        
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message = text_data_json['message']
        
        cmd_result = k8s_client.execute_command_in_pod(namespace='rl-platform', pod_name=self.pod_name, container_name=self.container_name, command=message)

        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'chat.message',
                'message': cmd_result
            }
        )
        pass
    
    async def chat_message(self, event):
        try:
            message = event['message']
            # print(f"log_message: {message}", file=sys.stderr)
            await self.send(text_data=json.dumps({
                'message': message
            }))
        except Exception as e:
            print(f"Error in log_message: {e}", file=sys.stderr)
            pass