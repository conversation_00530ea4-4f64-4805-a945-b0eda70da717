from rest_framework.response import Response
from rest_framework.parsers import MultiPartParser
from rest_framework.viewsets import ModelViewSet
from rest_framework.decorators import action
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_204_NO_CONTENT, HTTP_400_BAD_REQUEST
from django_filters.rest_framework import DjangoFilterBackend

from backend_api.models.evaluation.evaluation_test import EvaluationTest
from backend_api.serializers.evaluation.evaluation_test import EvaluationTestSerializer


class EvaluationTestViewSets(ModelViewSet):
    """
    评估测试视图集，提供评估测试管理的API接口
    包括：获取测试信息、创建测试、更新测试、删除测试、运行测试等
    """
    queryset = EvaluationTest.objects.all()
    serializer_class = EvaluationTestSerializer
    parser_classes = [MultiPartParser]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['evaluation', 'status', 'is_default']
    
    def get_queryset(self):
        """获取查询集，可根据用户权限过滤"""
        user = self.request.user
        queryset = self.queryset
        
        # 根据名称过滤
        name = self.request.query_params.get('name', '')
        if name:
            queryset = queryset.filter(name__icontains=name)
        
        # 根据评估ID过滤
        evaluation_id = self.request.query_params.get('evaluation_id', '')
        if evaluation_id:
            queryset = queryset.filter(evaluation__evaluation_id=evaluation_id)
            
        # 如果不是管理员，只能看到与自己创建的评估关联的测试
        if not user.is_staff:
            queryset = queryset.filter(evaluation__creater=user.id)
            
        return queryset
    
    def create(self, request):
        """创建评估测试"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            test = serializer.save()
            return Response(
                {"data": serializer.data, "msg": "测试创建成功", "code": 200}, 
                status=HTTP_201_CREATED
            )
        else:
            return Response(
                {"err_msg": serializer.errors, "msg": "参数错误", "code": 400}, 
                status=HTTP_400_BAD_REQUEST
            )
    
    def destroy(self, request, *args, **kwargs):
        """删除评估测试"""
        instance = self.get_object()
        
        # 不允许删除默认测试
        if instance.is_default:
            return Response(
                {"err_msg": "不能删除默认入库测试", "msg": "操作被拒绝", "code": "default_test"}, 
                status=HTTP_400_BAD_REQUEST
            )
            
        self.perform_destroy(instance)
        return Response({"msg": "测试删除成功", "code": 200}, status=HTTP_204_NO_CONTENT)
    
    @action(detail=True, methods=['post'])
    def run(self, request, pk=None):
        """运行评估测试"""
        test = self.get_object()
        
        # 检查测试状态
        if test.status == EvaluationTest.StatusChoice.RUNNING:
            return Response(
                {"err_msg": "测试正在运行中", "msg": "状态错误", "code": "test_running"}, 
                status=HTTP_400_BAD_REQUEST
            )
        
        # TODO: 实际测试运行逻辑，可以在这里调用异步任务
        # 此处应该调用Celery任务来执行测试
        
        # 更新测试状态为运行中
        test.status = EvaluationTest.StatusChoice.RUNNING
        test.save()
        
        return Response(
            {"msg": "测试已开始运行", "code": 200, "data": self.get_serializer(test).data}, 
            status=HTTP_200_OK
        ) 