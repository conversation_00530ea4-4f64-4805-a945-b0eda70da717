import json
import zipfile
from rest_framework import serializers
import yaml
from backend_api.models.scenario import <PERSON><PERSON><PERSON>


def check_zip_file(zip_file_path):
    with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
        # 获取zip文件中的文件列表
        file_list = zip_ref.namelist()

        # 检查是否包含 config.yaml 和 hyperparam.json
        if 'config.yaml' in file_list and 'infrl_training_config.json' in file_list:
            print("The zip file contains config.yaml and infrl_training_config.json.")
            return True
        else:
            print("The zip file does not contain either config.yaml or infrl_training_config.json.")
            return False

def read_zip_config_file(zip_file_path):
    with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
        # 读取config.yaml
        with zip_ref.open('config.yaml') as config_file:
            config_content = yaml.safe_load(config_file)

    return config_content

def read_zip_training_config_file(zip_file_path):
    with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
        # 读取config.yaml
        with zip_ref.open('infrl_training_config.json') as config_file:
            config_content = json.load(config_file)

    return config_content


class ScenarioSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = Scenario
        fields = "__all__"
        

class ScenarioCheckSerializer(serializers.ModelSerializer):
    config = serializers.SerializerMethodField()
    
    def get_config(self, scenario):
       
        try:
            file = scenario.get('file')
            config = read_zip_config_file(file)
        except:
            config = {}
        return config
    
    
    def validate_file(self, file):
        if not file.name.lower().endswith('.zip'):
            raise serializers.ValidationError(f"File '{file.name}' is not a Zip file.")
        
        if not check_zip_file(file):
            raise serializers.ValidationError(f"File '{file.name}' does not contain config.yaml or infrl_training_config.json.")
        
        return file
    
    class Meta:
        model = Scenario
        fields = ["file", "config"]
        

class ScenarioDetailSerializer(serializers.ModelSerializer):
    
    creater_name = serializers.SerializerMethodField()
    config = serializers.SerializerMethodField()
    training_config = serializers.SerializerMethodField()
    
    def get_config(self, scenario):
        try:
            config = read_zip_config_file(scenario.file)
        except:
            config = {
                "agents": [
                {
                    "id": 1,
                    "name": "car",
                    "observations": [
                        {
                            "id": 1,
                            "name": "观测1",
                            "description": "观测1: 自己观测信息",
                            "value": "observation1_1"
                        }
                    ],
                    "actions": [
                        {
                            "id": 1,
                            "name": "动作1",
                            "description": "动作1: 左右移动",
                            "value": "action1_1"
                        }
                    ]
                }
            ]
        }
        return config
    
    def get_training_config(self, scenario):
        try:
            config = read_zip_training_config_file(scenario.file)
        except:
            config = {}
        return config
    
    def get_creater_name(self, scenario):
        if scenario.creater:
            return scenario.creater.username
        return ""

    class Meta:
        model = Scenario
        fields = ["id", "name", "color", "creater", "simulator", "creater_name", "create_time", "file", "desc", "config", "training_config"]


class ScenarioCreateSerializer(serializers.ModelSerializer):
    
    def validate_file(self, file):
        if not file.name.lower().endswith('.zip'):
            raise serializers.ValidationError(f"File '{file.name}' is not a Zip file.")
        
        if not check_zip_file(file):
            raise serializers.ValidationError(f"File '{file.name}' does not contain config.yaml or infrl_training_config.json.")
        
        return file
    
    class Meta:
        model = Scenario
        fields = ["id", "name", "color", "creater", "simulator", "create_time", "file", "desc"]


