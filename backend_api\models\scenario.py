
import uuid
from django.db import models


class Scenario(models.Model):
    
    class ColorChoice(models.IntegerChoices):
        """
        颜色
        """
        WHITE = 0, '白色'
        BLACK = 1, '黑色'
        RED = 2, '红色'
        ORANGE = 3, '橙色'
        YELLO = 4, '黄色'
        GREEN = 5, '绿色'
        BLUE = 6, '蓝色'
        INDIGO = 7, '靛色'
        PURPLE = 8, '紫色'
    
    scenario_id = models.UUIDField('想定id', default=uuid.uuid4, editable=False)
    name = models.CharField('想定名称', max_length=255, blank=False)
    desc = models.CharField('想定描述', max_length=1024, null=True)
    address = models.CharField('想定存储地址', max_length=255, null=True)
    create_time = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    
    file = models.FileField('想定存储地址', upload_to='scenarios', null=True)
    config_file = models.FileField('想定配置文件存储地址', upload_to='scenarios/configs', null=True)
    color = models.SmallIntegerField('想定颜色', null=True, choices=ColorChoice.choices, default=ColorChoice.WHITE)
    
    run_image = models.CharField('run镜像', max_length=255, null=True)
    run_args = models.CharField('run启动命令', max_length=1024, null=True)
    run_result_path = models.CharField('run结果路径', max_length=255, null=True)
    
    infrl_training_config = models.JSONField('训练配置', null=True)
    
    creater = models.ForeignKey('user.User', on_delete=models.CASCADE, verbose_name='创建用户', db_constraint=False, null=True)
    simulator = models.ForeignKey('backend_api.Simulator', on_delete=models.CASCADE, verbose_name='想定对应的仿真器', db_constraint=False, null=True)
    
    is_deleted = models.BooleanField('是否删除', default=False)
    deleted_time = models.DateTimeField('删除时间', null=True)
    
    class Meta:
        verbose_name = '想定实例'
        verbose_name_plural = verbose_name
        ordering = ('-id',)