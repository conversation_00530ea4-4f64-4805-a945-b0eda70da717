import datetime
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework.generics import GenericAPIView
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_400_BAD_REQUEST
from django_filters.rest_framework import DjangoFilterBackend
from kubernetes.client.rest import ApiException
from backend_api.models.algorithm import Algorithm
from backend_api.models.model import Model
from backend_api.models.task import Task
from backend_api.serializers.model import ModelSerializer
from backend_api.serializers.task import (
    TaskDetailSerializer,
    TaskListSerializer,
    TaskLogSerializer,
    TaskResumeSerializer,
    TaskSerializer,
    TaskReleaseSerializer,
    TaskSuspendingSerializer,
)
from utils import k8s_client, ray_utils, task_utils
from backend_api.models.task_factory import TaskFactory


class TaskViewSets(ModelViewSet):
    """
    声明用户资源类 用户操作:获取任务信息  更新任务  删除任务 创建任务
    """

    queryset = Task.objects.all()
    serializer_class = TaskSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["status"]

    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            name = self.request.query_params.get("name", "")
            if name and name != "":
                queryset = (
                    Task.objects.filter(name__icontains=name)
                    .filter(is_deleted=False)
                    .all()
                )
            else:
                queryset = Task.objects.filter(is_deleted=False).all()
                # queryset = Task.objects.all()
        else:
            name = self.request.query_params.get("name", "")
            if name and name != "":
                queryset = (
                    Task.objects.filter(owner=user.id)
                    .filter(name__icontains=name)
                    .filter(is_deleted=False)
                    .all()
                )
            else:
                # 根据 owner 过滤查询集
                queryset = (
                    Task.objects.filter(owner=user.id).filter(is_deleted=False).all()
                )
                # queryset = Task.objects.filter(owner=user.id).all()

        return queryset

    def create(self, request):
        user = request.user

        # 检查任务名称是否存在
        name = request.data["name"]
        if Task.objects.filter(name=name).count() > 0:
            task = Task.objects.filter(name=name).first()
            if task and not task.is_deleted:
                return Response(
                    {
                        "err_msg": f"task {name} 已存在",
                        "msg": "对象已存在错误",
                        "code": "object_exists",
                    },
                    status=HTTP_400_BAD_REQUEST,
                )

        # 获取任务类型，默认为训练任务
        task_type = request.data.get("task_type", Task.TaskType.TRAINING)
        
        # 准备任务基本数据
        task_data = {
            "name": name,
            "owner": user.id,
            "status": Task.StatusChoice.INITIALIZED,
            "task_type": task_type
        }

        # 准备特定任务类型的数据
        specific_data = {}
        
        try:
            if task_type == Task.TaskType.TRAINING:
                # 处理训练任务特定数据
                algo = request.data.get("algorithm")
                if not algo:
                    algo = Algorithm.objects.first().id
                specific_data["algorithm"] = algo
                
                # 处理模型数据
                model_name = request.data.get("model_name")
                if model_name and model_name != "":
                    model_seria = ModelSerializer(
                        data={
                            "name": model_name,
                            "creater": user.id,
                        }
                    )
                    if not model_seria.is_valid():
                        return Response(
                            {
                                "err_msg": model_seria.errors,
                                "msg": "参数错误",
                                "code": "invalid_model_name",
                            },
                            status=HTTP_400_BAD_REQUEST,
                        )
                    model = model_seria.save()
                    specific_data["model"] = model.id
                else:
                    specific_data["model"] = request.data.get("model")
                
                # 添加其他训练特定字段
                for field in ["entrypoint", "mount_path", "tb_url", "actor_num", "learner_num", 
                            "actor_per_cpu", "actor_per_gpu", "actor_per_memory",
                            "learner_per_cpu", "learner_per_gpu", "learner_per_memory"]:
                    if field in request.data:
                        specific_data[field] = request.data[field]

            elif task_type == Task.TaskType.DEDUCTION:
                # 处理推演任务特定数据
                required_fields = ["model", "entrypoint", "mount_path"]
                for field in required_fields:
                    if field not in request.data:
                        return Response(
                            {
                                "err_msg": f"缺少必要字段: {field}",
                                "msg": "参数错误",
                                "code": "missing_required_field",
                            },
                            status=HTTP_400_BAD_REQUEST,
                        )
                    specific_data[field] = request.data[field]
                
                # 添加可选字段
                optional_fields = ["desc"]
                for field in optional_fields:
                    if field in request.data:
                        specific_data[field] = request.data[field]

            elif task_type == Task.TaskType.EVALUATION:
                # 处理评估任务特定数据
                required_fields = ["model", "evaluation"]
                for field in required_fields:
                    if field not in request.data:
                        return Response(
                            {
                                "err_msg": f"缺少必要字段: {field}",
                                "msg": "参数错误",
                                "code": "missing_required_field",
                            },
                            status=HTTP_400_BAD_REQUEST,
                        )
                    specific_data[field] = request.data[field]

            # 使用工厂类创建任务及其特定配置
            task, specific_task = TaskFactory.create_task(
                task_type=task_type,
                task_data=task_data,
                specific_data=specific_data
            )

            # 根据任务类型创建相应的 Ray 作业
            if task_type == Task.TaskType.TRAINING:
                # task_utils.create_ray_training(task)
                # task_utils.create_local_training(task)
                task_utils.create_docker_training(task)
            elif task_type == Task.TaskType.DEDUCTION:
                # task_utils.create_ray_deduction(task)
                pass
            elif task_type == Task.TaskType.EVALUATION:
                # task_utils.create_ray_evaluation(task)
                pass

            return Response(
                {
                    "data": self.get_serializer(task).data,
                    "msg": "create task success.",
                    "code": 200,
                },
                status=HTTP_201_CREATED,
            )

        except ApiException as e:
            # 发生错误时，标记任务为删除状态
            if 'task' in locals():
                task.is_deleted = True
                task.status = Task.StatusChoice.TERMINATING
                task.save(update_fields=["status", "is_deleted"])

            if e.status == 409:
                return Response(
                    {"err_msg": str(e), "msg": "对象已存在", "code": "object_exists"},
                    status=HTTP_400_BAD_REQUEST,
                )
            elif e.status == 422:
                return Response(
                    {
                        "err_msg": str(e),
                        "msg": "参数错误",
                        "code": "invalid_task_params",
                    },
                    status=HTTP_400_BAD_REQUEST,
                )
            
            return Response(
                {
                    "err_msg": str(e),
                    "msg": "创建任务失败",
                    "code": "create_task_failed",
                },
                status=HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response(
                {
                    "err_msg": str(e),
                    "msg": "创建任务失败",
                    "code": "create_task_failed",
                },
                status=HTTP_400_BAD_REQUEST,
            )

    def retrieve(self, request, *args, **kwargs):
        task = self.get_object()
        serializer = TaskDetailSerializer(task)

        return Response({"data": serializer.data, "code": 200}, status=HTTP_200_OK)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = TaskListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = TaskListSerializer(queryset, many=True)

        return Response({"data": serializer.data, "code": 200}, status=HTTP_200_OK)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()

        # super().destroy(request, *args, **kwargs)

        instance.status = Task.StatusChoice.TERMINATING
        instance.save(update_fields=["status"])

        return Response({"code": 200, "msg": "delete success."}, status=HTTP_200_OK)


class TaskReleaseView(GenericAPIView):
    """
    根据任务名， 释放任务
    """

    queryset = Task.objects.all()
    serializer_class = TaskReleaseSerializer

    def post(self, request):
        task_name = request.data.get("name")

        if task_name is not None:
            task = self.queryset.filter(name=task_name).first()
            if task:
                task.status = Task.StatusChoice.TERMINATING
                task.save(update_fields=["status"])
                seria = self.get_serializer(task)
                return Response(
                    {"code": 200, "data": seria.data, "msg": "release success."},
                    status=HTTP_200_OK,
                )

        return Response(
            {"code": 400, "err_msg": "please send correct id."},
            status=HTTP_400_BAD_REQUEST,
        )


class TaskSuspendingView(GenericAPIView):
    """
    根据任务名， 释放任务
    """

    queryset = Task.objects.all()
    serializer_class = TaskSuspendingSerializer

    def post(self, request):
        task_id = request.data.get("task_id")

        if task_id is not None:
            task = self.queryset.filter(task_id=task_id).first()
            if task:
                task.status = Task.StatusChoice.PAUSING
                task.end_time = datetime.datetime.now()
                task.save(update_fields=["status", "end_time"])
                seria = self.get_serializer(task)

                return Response(
                    {"code": 200, "data": seria.data, "msg": "suspending success."},
                    status=HTTP_200_OK,
                )

        return Response(
            {"code": 400, "err_msg": "please send correct task_id."},
            status=HTTP_400_BAD_REQUEST,
        )


class TaskResumeView(GenericAPIView):
    """
    根据任务名， 释放任务
    """

    queryset = Task.objects.all()
    serializer_class = TaskResumeSerializer

    def post(self, request):
        user = request.user
        task_id = request.data.get("task_id")

        if task_id is not None:
            task = self.queryset.filter(task_id=task_id).first()
            if task:
                task.status = Task.StatusChoice.RESUMING
                task.save(update_fields=["status"])
                seria = self.get_serializer(task)

                return Response(
                    {"code": 200, "data": seria.data, "msg": "resume success."},
                    status=HTTP_200_OK,
                )

        return Response(
            {"code": 400, "err_msg": "please send correct task_id."},
            status=HTTP_400_BAD_REQUEST,
        )


class TaskLogView(GenericAPIView):
    """
    根据任务id, pod名, 获取任务日志
    """

    queryset = Task.objects.all()
    serializer_class = TaskLogSerializer

    def post(self, request):
        task_id = request.data.get("task_id")
        pod_name = request.data.get("pod_name")

        if task_id is not None:
            task = self.queryset.filter(task_id=task_id).first()
            if task:
                # 获取learner日志
                pod_log = k8s_client.get_pod_logs("rl-platform", pod_name, "learner")

                for line in pod_log.stream():
                    print(line.decode("utf-8"), end="")

                return Response(
                    {"code": 200, "data": {"pod_log": pod_log}}, status=HTTP_200_OK
                )

        return Response(
            {"code": 400, "err_msg": "please send correct task_id pod_name."},
            status=HTTP_400_BAD_REQUEST,
        )
