import uuid
from django.db import models


class Evaluation(models.Model):
    """
    评估管理模型
    评估代码是承上启下的位置，指导如何运行仿真引擎，策略该如何定义，以及如何与仿真引擎交互。
    一个典型的评估代码的例子是各类智能体比赛中的策略评估代码，用于指定选手策略读取，在仿真中运行后输出胜负结果。
    """
    
    class StatusChoice(models.IntegerChoices):
        """状态choice"""
        ACTIVE = 0, '生效中'
        DEPRECATED = 1, '已弃用'
        DEVELOPING = 2, '开发中'
    
    evaluation_id = models.UUIDField('评估ID', default=uuid.uuid4, editable=False)
    name = models.CharField('评估名称', max_length=255, unique=True)
    type = models.CharField('评估类型', max_length=50, blank=True, default='')
    description = models.CharField('评估描述', max_length=1024, null=True, blank=True)
    
    # 评估代码文件存储
    code_file = models.FileField('评估代码', upload_to='evaluations/code', null=True, blank=True)
    
    # 评估状态
    status = models.IntegerField('状态', choices=StatusChoice.choices, default=StatusChoice.DEVELOPING)
    
    # 环境依赖描述
    environment = models.CharField('环境依赖', max_length=1024, null=True, blank=True)
    
    # 仿真引擎信息
    simulation_engine = models.CharField('仿真引擎', max_length=255, null=True, blank=True)
    
    # 策略规范说明，描述策略应该如何实现
    policy_spec = models.TextField('策略规范', null=True, blank=True)
    
    # GitLab 项目信息
    gitlab_project_id = models.IntegerField('GitLab项目ID', null=True, blank=True)
    gitlab_project_url = models.URLField('GitLab项目URL', max_length=1024, null=True, blank=True)
    
    # 时间信息
    create_time = models.DateTimeField('创建时间', auto_now_add=True)
    update_time = models.DateTimeField('更新时间', auto_now=True)
    
    # 关联用户
    creater = models.ForeignKey('user.User', on_delete=models.CASCADE, verbose_name='创建用户', db_constraint=False, null=True)
    
    class Meta:
        verbose_name = '评估'
        verbose_name_plural = verbose_name
        ordering = ('-id',) 