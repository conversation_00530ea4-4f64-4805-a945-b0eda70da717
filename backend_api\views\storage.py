import os
import tempfile
from django import forms
from django.http import HttpResponse
from rest_framework.response import Response
from rest_framework.generics import GenericAPIView

from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_204_NO_CONTENT, HTTP_400_BAD_REQUEST
from backend_api.serializers.storage import StorageUploadSerializer, StorageDeleteSerializer, StorageDownloadSerializer, StorageCreateDirSerializer, StorageListSerializer

from utils import k8s_client


class StorageListView(GenericAPIView):
    
    serializer_class = StorageListSerializer
    
    def get(self, request):
        
        user = request.user
        file_url = request.query_params.get("url")
        if not file_url:
            file_url = '/'
        
        file_lists = k8s_client.list_pvc_files("rl-platform", user.username, file_url)
        name = request.query_params.get("name")
        if name:
            file_lists = [f for f in file_lists if name in f.get('name')]
        
        return Response({"code": 200, "data": {"url": file_url, "files": file_lists}, "msg": "list success."}, status=HTTP_200_OK)


class StorageUploadView(GenericAPIView):
    
    serializer_class = StorageUploadSerializer
    
    def post(self, request):
        
        user = request.user
        url = request.data.get("url")
        files = request.FILES.getlist('files')
        
        if not url or not files:
            return Response({"code": 400, "err_msg": "please send correct url and files."}, status=HTTP_400_BAD_REQUEST)
        
        # 创建临时文件夹  
        temp_dir = tempfile.TemporaryDirectory()
        source_files = []
        paths = []
        
        # 遍历文件列表，将文件写入临时文件夹  
        for file in files:  
            with open(os.path.join(temp_dir.name, file.name), 'wb') as temp_file:  
                temp_file.write(file.read())
                
                source_file = os.path.join(temp_dir.name, temp_file.name)
                source_files.append(source_file)
                
        for sf in source_files:
            if url == '/':
                file_url = f'/{user.username}' + f'/{file.name}'
                ret_path = f'/{file.name}'
            else:
                file_url = f'/{user.username}' + url + f'/{file.name}'
                ret_path = url + f'/{file.name}'
            
            _ = k8s_client.upload_to_pvc("rl-platform", user.username, file_url, sf)
            paths.append(ret_path)
                
        return Response({"code": 200, "data": {"paths": ret_path}, "msg": "uploaded success."}, status=HTTP_200_OK)
    
    
class StorageCreateDirView(GenericAPIView):
    
    serializer_class = StorageCreateDirSerializer
    
    def post(self, request):
        
        user = request.user
        url = request.data.get("url")
        dir_name = request.data.get("dir")
        
        if not url or not dir_name:
            return Response({"code": 400, "err_msg": "please send correct url and dir_name."}, status=HTTP_400_BAD_REQUEST)
        
        file_url = f'/{user.username}' + url
        
        _ = k8s_client.create_pvc_dir("rl-platform", user.username, file_url, dir_name)
        if url == '/':
            dir_path = f'/{dir_name}'
        else:
            dir_path = url + '/' + dir_name
        return Response({"code": 200, "data": {"dir_path": dir_path}, "msg": "create dir success."}, status=HTTP_200_OK)

    
class StorageDeleteView(GenericAPIView):
    
    serializer_class = StorageDeleteSerializer
    
    def post(self, request):
        
        user = request.user
        url = request.data.get("url")
        
        if url == '/':
            file_url = f'/{user.username}'
        else:
            file_url = f'/{user.username}' + url
        
        if not url:
            return Response({"code": 400, "err_msg": "please send correct url."}, status=HTTP_400_BAD_REQUEST)
        
        del_result = k8s_client.delete_pvc_file("rl-platform", user.username, file_url)
        if del_result:
            return Response({"code": 200, "msg": "delete success."}, status=HTTP_200_OK)
        else:
            return Response({"code": "delete_failed", "err_msg": "delete failed.", "msg": "delete failed."}, status=HTTP_400_BAD_REQUEST)
        
        
class StorageDownloadView(GenericAPIView):
    
    serializer_class = StorageDownloadSerializer
    
    def post(self, request):
        
        user = request.user
        url = request.data.get("url")
        
        if url == '/':
            file_url = f'/{user.username}'
        else:
            file_url = f'/{user.username}' + url
        
        if not url:
            return Response({"code": 400, "err_msg": "please send correct url."}, status=HTTP_400_BAD_REQUEST)
        
        file_path = k8s_client.download_from_pvc("rl-platform", user.username, file_url)
        
        if file_path:
            with open(file_path, 'rb') as f:
                response = HttpResponse(f.read(), content_type='application/octet-stream')
                response['Content-Disposition'] = f'attachment;filename="{os.path.basename(file_path)}"'
                return response
        else:
            return Response({"code": "empty_dir_delete_failed", "err_msg": "download failed. empty dir.", "msg": "download failed."}, status=HTTP_400_BAD_REQUEST)
