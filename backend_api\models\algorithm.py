from datetime import timezone
import uuid
import logging
from django.db import models

logger = logging.getLogger(__name__)

class Algorithm(models.Model):
    
    algorithm_id = models.UUIDField('算法id', default=uuid.uuid4, editable=False, null=True, blank=True)
    name = models.Char<PERSON>ield('算法名称', max_length=255, unique=True)
    artifact_name = models.CharField('镜像名称', max_length=255, unique=True)
    desc = models.CharField('算法描述', max_length=1024, null=True)
    
    # 关联环境信息
    environment = models.ForeignKey(
        'backend_api.Environment',
        on_delete=models.PROTECT,
        verbose_name='关联环境',
        related_name='algorithms',
        null=True,
        help_text='算法关联的仿真环境'
    )
    
    # 代码仓库信息
    gitlab_url = models.URLField(
        'GitLab仓库地址',
        max_length=1024,
        null=True,
        blank=True,
        help_text='环境相关代码的GitLab仓库地址'
    )
    
    create_time = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    creater = models.ForeignKey('user.User', on_delete=models.CASCADE, verbose_name='创建用户', db_constraint=False, null=True)
    
    class Meta:
        verbose_name = '算法实例'
        verbose_name_plural = verbose_name
        ordering = ('-id',)
        
    def __str__(self):
        env_name = self.environment.name if self.environment else "未关联环境"
        return f"{self.name} (环境: {env_name})"
    
    def save(self, *args, **kwargs):
        # 确保 algorithm_id 是有效的 UUID
        if not self.algorithm_id:
            self.algorithm_id = uuid.uuid4()
        elif isinstance(self.algorithm_id, str):
            try:
                self.algorithm_id = uuid.UUID(self.algorithm_id)
            except (ValueError, AttributeError, TypeError):
                logger.warning(f"Invalid UUID detected for algorithm {self.name}, generating new one")
                self.algorithm_id = uuid.uuid4()
        super().save(*args, **kwargs)
    
    @property
    def algorithm_id_str(self):
        """
        安全地获取 algorithm_id 的字符串表示
        """
        try:
            return str(self.algorithm_id) if self.algorithm_id else None
        except (ValueError, AttributeError, TypeError):
            return None