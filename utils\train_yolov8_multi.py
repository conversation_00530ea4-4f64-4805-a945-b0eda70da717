#!/usr/bin/env python3
"""
华为NPU单机多卡YOLO训练脚本
支持单机多/单卡训练
"""

import os
import sys
import time
import json
import logging
import psutil
import argparse
import subprocess
from pathlib import Path
import torch
import cv2
import numpy as np
from datetime import datetime
import requests


# # 添加当前目录到Python路径
# ultralytics_dir = '/root/siton-data-c16a16a2cdc44452a1c4267121b485aa/data/ultralytics_v8'
# sys.path.insert(0, str(ultralytics_dir))
# # 设置环境变量
# os.environ["PYTHONPATH"] = str(ultralytics_dir)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def detect_device():
    """
    检测可用的训练设备，优先使用NPU
    返回: 设备标识符 ('cpu', 'cuda:0', 或 'npu:0,1,2,3')
    """
    try:
        # 首先检查NPU
        try:
            import torch_npu
            if torch_npu.npu.is_available():
                npu_count = torch_npu.npu.device_count()
                logger.info(f"发现 {npu_count} 个可用的NPU设备")

                # 根据NPU数量返回设备配置
                if npu_count > 1:
                    device = f"npu:{','.join(map(str, range(npu_count)))}"
                    print(f"🔄 自动配置: 使用所有{npu_count}个NPU - {device}")
                    return device
                else:
                    print("🔄 自动配置: 使用单个NPU - npu:0")
                    return 'npu:0'
        except ImportError:
            logger.warning("未安装torch_npu，跳过NPU检测")
            pass
        except Exception as e:
            logger.warning(f"NPU检测失败: {e}")
            pass

        # 检查GPU
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            if device_count > 0:
                gpu_name = torch.cuda.get_device_name(0)
                logger.info(f"使用GPU: {gpu_name}")
                if device_count > 1:
                    return f"cuda:{','.join(map(str, range(device_count)))}"
                else:
                    return 'cuda:0'
        logger.info("使用CPU进行训练")
        return 'cpu'
    except Exception as e:
        logger.warning(f"设备检测出错: {e}")
        return 'cpu'

def get_resource_usage():
    """
    获取系统资源使用情况
    返回: 包含CPU、内存、GPU和NPU使用率的字典
    """
    usage = {
        'cpu_usage': psutil.cpu_percent(),
        'memory_usage': psutil.virtual_memory().percent,
        'gpu_usage': 0.0,
        'npu_usage': 0.0,
        'timestamp': time.time()
    }

    # 获取GPU使用率
    try:
        if torch.cuda.is_available():
            usage['gpu_usage'] = float(torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated() * 100)
    except:
        pass

    # 获取NPU使用率
    try:
        result = subprocess.check_output(['npu-smi', 'info'], universal_newlines=True)
        for line in result.split('\n'):
            if '910' in line and 'OK' in line:
                parts = line.split('|')
                if len(parts) >= 4:
                    power_info = parts[3].strip()
                    if 'W' in power_info:
                        power = float(power_info.split('W')[0].strip())
                        usage['npu_usage'] = (power / 250.0) * 100  # 假设最大功率250W
    except:
        pass

    return usage

def parse_training_config(config, dataset_path=None, device=None):
    """
    解析训练配置，转换为YOLOv8可接受的参数格式
    """
    print("config", config)
    # 基础训练参数
    train_args = {
        'epochs': int(config['parameters']['epochs']), # 训练轮数
        'device': device or detect_device(), # 训练设备
        'project': 'runs_detect', # 训练结果保存路径
        'name': f'task_{config.get("id", "npu_train")}', # 训练结果保存名称
        'exist_ok': True, # 是否覆盖训练结果
        'plots': False,  # 禁用绘图以提高性能
    }

    # 数据集路径 - 优先使用命令行参数提供的路径
    if dataset_path:
        train_args['data'] = dataset_path
    else:
        # 数据集和验证参数
        dataset_dir = os.path.join('datasets', config['training']['dataset']['name']) # 数据集路径
        train_args['data'] = os.path.join(dataset_dir, 'data.yaml') # 数据集路径

    # 批次大小和学习率
    train_args['batch'] = int(config['parameters'].get('batchSize', 128)) # 批次大小
    train_args['lr0'] = float(config['parameters'].get('learningRate', 0.01)) # 学习率

    # 图像尺寸
    train_args['imgsz'] = int(config['parameters'].get('imageSize', 640)) # 图像尺寸

    # 优化器相关参数
    optimizer = config['otherParams'].get('optimizer', 'SGD').lower() # 优化器
    train_args['optimizer'] = optimizer # 优化器

    if optimizer in ['sgd', 'adam', 'adamw']:
        train_args['momentum'] = float(config['otherParams'].get('momentum', 0.937)) # 动量
        train_args['weight_decay'] = float(config['otherParams'].get('weightDecay', 0.0005)) # 权重衰减

    # 早停和检查点
    if 'earlyStopping' in config['otherParams']:
        train_args['patience'] = int(config['otherParams']['earlyStopping'])

    if 'checkpointFreq' in config['otherParams']:
        train_args['save_period'] = int(config['otherParams']['checkpointFreq'])

    # 混合精度训练
    if config['otherParams'].get('useMixedPrecision', False):
        train_args['amp'] = True  # 自动混合精度

    # 学习率策略
    lr_strategy = config['parameters'].get('learningRateStrategy', '').lower()
    if lr_strategy == '余弦衰减':
        train_args['cos_lr'] = True  # 使用余弦学习率调度

    # 标签平滑
    if 'labelSmoothing' in config['otherParams']:
        train_args['label_smoothing'] = float(config['otherParams']['labelSmoothing'])

    # Dropout
    if 'dropout' in config['otherParams']:
        train_args['dropout'] = float(config['otherParams']['dropout'])

    # 预热步数
    if 'warmupSteps' in config['otherParams']:
        warmup_epochs = int(config['otherParams']['warmupSteps']) // (train_args['batch'] * 100)  # 估算预热轮数
        train_args['warmup_epochs'] = max(1, warmup_epochs)  # 至少1轮
        train_args['warmup_momentum'] = 0.8
        train_args['warmup_bias_lr'] = 0.1

    # 数据加载器配置
    train_args['workers'] = int(config.get('resources', {}).get('workers', 8))

    # 缓存配置
    if config['otherParams'].get('useCache', False):
        train_args['cache'] = True

    return train_args

def setup_npu_environment():
    """检查NPU训练环境"""
    try:
        import torch_npu
        print(f"✅ torch_npu版本: {torch_npu.__version__}")

        # 检查NPU可用性
        if not torch_npu.npu.is_available():
            raise RuntimeError("NPU不可用，请检查驱动和torch_npu安装")

        npu_count = torch_npu.npu.device_count()
        print(f"✅ 检测到 {npu_count} 个NPU设备")

        # 显示NPU信息
        for i in range(npu_count):
            try:
                device_name = torch_npu.npu.get_device_name(i)
                device_props = torch_npu.npu.get_device_properties(i)
                memory_gb = device_props.total_memory / 1024**3
                print(f"   NPU:{i} - {device_name} ({memory_gb:.1f}GB)")
            except Exception as e:
                print(f"   NPU:{i} - 设备信息获取失败: {e}")

        return npu_count

    except ImportError:
        print("⚠️  torch_npu未安装，将使用其他可用设备")
        return 0
    except Exception as e:
        print(f"⚠️  NPU环境检查失败: {e}")
        return 0

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='华为NPU多机多卡YOLO训练')

    # 配置文件参数
    parser.add_argument('--config', type=str, default='training_config.json', help='训练配置文件路径')
    parser.add_argument('--task_id', type=str, help='任务ID，用于生成输出目录名')

    # 基本训练参数 (可覆盖配置文件)
    parser.add_argument('--model', type=str, help='模型文件路径，优先级高于配置文件')
    parser.add_argument('--data', type=str, help='数据集配置文件，优先级高于配置文件')

    # 环境路径参数
    parser.add_argument('--mount_path', type=str, help='数据挂载路径')
    parser.add_argument('--ultralytics_dir', type=str, help='ultralytics库路径')

    return parser.parse_args()

def single_machine_train(train_args, model_path, device):
    """单机训练（单卡或多卡）"""
    # ultralytics已经在main函数中导入，这里直接使用
    from ultralytics import YOLO

    print("=" * 60)
    print("🚀 启动单机NPU训练")
    print(f"📱 设备: {device}")
    print(f"📊 批次大小: {train_args['batch']}")
    print(f"🔄 训练轮次: {train_args['epochs']}")
    print("=" * 60)

    # 创建模型
    model = YOLO(model_path)

    # 获取任务名称
    task_name = train_args.get('name', 'task_default')

    # 添加训练回调函数
    def on_train_epoch_end(trainer):
        """每个训练周期结束时的回调函数"""
        nonlocal task_name  # 使用闭包访问外部变量
        
        # 获取资源使用情况
        resource_metrics = get_resource_usage()
        
        # 安全获取metrics，如果为None则使用空字典
        metrics = trainer.metrics if trainer.metrics is not None else {}
        
        # 从trainer.tloss获取训练损失组件
        if hasattr(trainer, 'tloss') and len(trainer.tloss) >= 3:
            # 将tensor转换为Python的float值
            box_loss = round(float(trainer.tloss[0]), 4)
            cls_loss = round(float(trainer.tloss[1]), 4)
            dfl_loss = round(float(trainer.tloss[2]), 4)
        else:
            # 如果tloss不可用，使用默认值
            box_loss = 0.0
            cls_loss = 0.0
            dfl_loss = 0.0

        # 合并训练指标和资源使用指标
        combined_metrics = {
            'epoch': trainer.epoch,
            'train/box_loss': box_loss,
            'train/cls_loss': cls_loss,
            'train/dfl_loss': dfl_loss,
            'metrics/precision': metrics.get('metrics/precision(B)', 0.0),
            'metrics/recall': metrics.get('metrics/recall(B)', 0.0),
            'metrics/mAP50': metrics.get('metrics/mAP50(B)', 0.0),
            'metrics/mAP50-95': metrics.get('metrics/mAP50-95(B)', 0.0),
            **resource_metrics
        }

        # 记录日志
        logger.info(f"Epoch {trainer.epoch} metrics: {combined_metrics}")

        # 保存指标到文件
        try:
            with open("/workspace/training_metrics.json", 'a') as f:
                f.write(json.dumps(combined_metrics) + '\n')
        except Exception as e:
            logger.error(f"保存训练指标失败: {e}")
        
        # 保存当前模型信息
        try:
            time.sleep(5)
            # 获取当前epoch的模型路径
            save_dir = trainer.save_dir if hasattr(trainer, 'save_dir') else 'runs_detect'
            weights_dir = os.path.join(save_dir, 'weights')
            
            # 查找当前epoch的特定模型文件
            current_epoch = trainer.epoch
            epoch_model_pattern = f"epoch{current_epoch}"
            
            # 列出weights目录下的所有文件
            if os.path.exists(weights_dir):
                model_files = [f for f in os.listdir(weights_dir) if f.endswith('.pt') and not f == 'last.pt']
                
                # 寻找当前epoch对应的模型文件
                epoch_model_file = None
                for model_file in model_files:
                    if epoch_model_pattern in model_file:
                        epoch_model_file = model_file
                        break
                
                if epoch_model_file:
                    # 构建完整路径
                    epoch_model_path = os.path.join(weights_dir, epoch_model_file)
                    
                    # 构建模型信息
                    model_info = get_model_info(epoch_model_path, metrics)
                    # 添加epoch信息
                    model_info['epoch'] = trainer.epoch
                    
                    # 检查是否是最佳模型
                    best_pt_path = os.path.join(weights_dir, 'best.pt')
                    if os.path.exists(best_pt_path):
                        # 检查文件修改时间，如果best.pt是刚更新的，说明当前epoch产生了最佳模型
                        best_mod_time = os.path.getmtime(best_pt_path)
                        epoch_mod_time = os.path.getmtime(epoch_model_path)
                        time_diff = abs(best_mod_time - epoch_mod_time)
                        if time_diff < 10:  # 如果修改时间相差不到10秒
                            model_info['is_best'] = True
                    
                    # 检查是否已经存在相同model_name的记录
                    model_name = model_info['model_name']
                    existing_model = check_existing_model_info(model_name)
                    if existing_model:
                        logger.info(f"模型 {model_name} 已存在于记录中，跳过保存")
                        return
                    
                    # 保存模型信息
                    logger.info(f"保存Epoch {trainer.epoch}的模型信息: {epoch_model_file}")
                    save_model_info_to_json(model_info, task_name)
                else:
                    # 如果找不到特定的epoch模型文件，尝试等待几秒后重新获取
                    # 可能是因为模型文件正在被写入磁盘，需要等待
                    logger.info(f"找不到Epoch {trainer.epoch}的模型文件，等待5秒后重试...")
                    time.sleep(5)  # 等待5秒
                    
                    # 重新扫描目录
                    if os.path.exists(weights_dir):
                        model_files = [f for f in os.listdir(weights_dir) if f.endswith('.pt') and not f == 'last.pt']
                        
                        # 重新寻找当前epoch对应的模型文件
                        epoch_model_file = None
                        for model_file in model_files:
                            if epoch_model_pattern in model_file:
                                epoch_model_file = model_file
                                break
                        
                        if epoch_model_file:
                            # 找到了模型文件，处理它
                            epoch_model_path = os.path.join(weights_dir, epoch_model_file)
                            
                            # 构建模型信息
                            model_info = get_model_info(epoch_model_path, metrics)
                            model_info['epoch'] = trainer.epoch
                            
                            # 检查是否是最佳模型
                            best_pt_path = os.path.join(weights_dir, 'best.pt')
                            if os.path.exists(best_pt_path):
                                best_mod_time = os.path.getmtime(best_pt_path)
                                epoch_mod_time = os.path.getmtime(epoch_model_path)
                                time_diff = abs(best_mod_time - epoch_mod_time)
                                if time_diff < 10:  # 如果修改时间相差不到10秒
                                    model_info['is_best'] = True
                            
                            # 检查是否已经存在相同model_name的记录
                            model_name = model_info['model_name']
                            existing_model = check_existing_model_info(model_name)
                            if existing_model:
                                logger.info(f"模型 {model_name} 已存在于记录中，跳过保存")
                                return
                            
                            # 保存模型信息
                            logger.info(f"重试成功：保存Epoch {trainer.epoch}的模型信息: {epoch_model_file}")
                            save_model_info_to_json(model_info, task_name)
                            return
                    
                    # 如果找不到特定的epoch模型文件，尝试找最近修改的非last.pt文件
                    if model_files:
                        # 按修改时间排序
                        model_files.sort(key=lambda f: os.path.getmtime(os.path.join(weights_dir, f)), reverse=True)
                        recent_model = model_files[0]
                        recent_model_path = os.path.join(weights_dir, recent_model)
                        
                        # 确认是最近创建的（在过去5分钟内）
                        if time.time() - os.path.getmtime(recent_model_path) < 300:  # 5分钟 = 300秒
                            model_info = get_model_info(recent_model_path, metrics)
                            model_info['epoch'] = trainer.epoch
                            
                            # 检查是否是最佳模型
                            if 'best' in recent_model:
                                model_info['is_best'] = True
                                
                            logger.info(f"找不到特定epoch模型，使用最近的模型: {recent_model}")
                            save_model_info_to_json(model_info, task_name)
                        else:
                            logger.warning(f"找不到Epoch {trainer.epoch}的模型文件，也没有找到最近创建的模型")
                    else:
                        logger.warning(f"没有找到任何模型文件")
            else:
                logger.warning(f"模型权重目录不存在: {weights_dir}")
                
        except Exception as e:
            logger.error(f"保存Epoch {trainer.epoch}模型信息失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

    # 注册回调函数
    model.add_callback('on_train_epoch_end', on_train_epoch_end)

    # 更新设备配置
    train_args['device'] = device
    train_args['verbose'] = True
    train_args['save'] = True
    train_args['plots'] = True
    train_args['val'] = True
    train_args['save_period'] = 1  # 每轮保存一次

    print("🎯 开始训练...")
    try:
        results = model.train(**train_args)
        print("✅ 训练完成！")
        print(f"📈 结果保存在: {model.trainer.save_dir}")
        return results
    except Exception as e:
        logger.error(f"训练失败: {e}")
        raise

def get_model_info(model_path, metrics):
    """
    获取模型信息
    Args:
        model_path: 模型文件路径
        metrics: 训练指标
    Returns:
        dict: 模型信息
    """
    # 确保model_path是绝对路径
    model_path = os.path.abspath(model_path)
    
    model_info = {
        'model_name': os.path.basename(model_path),
        'model_path': model_path,
        'accuracy': 0.0,
        'precision': 0.0,
        'recall': 0.0,
        'inference_speed': 0.0,
        'model_size_mb': 0.0,
        'timestamp': datetime.now().isoformat()
    }
    
    try:
        # 获取模型大小
        if os.path.exists(model_path):
            file_size = os.path.getsize(model_path)
            model_info['model_size_mb'] = round(file_size / (1024 * 1024), 2)
            logger.info(f"模型大小: {model_info['model_size_mb']} MB")
        else:
            logger.warning(f"模型文件不存在: {model_path}")
        
        # 从metrics中获取准确率、精度、召回率
        model_info['accuracy'] = round(metrics.get('metrics/mAP50(B)', 0.0), 4)
        model_info['precision'] = round(metrics.get('metrics/precision(B)', 0.0), 4)
        model_info['recall'] = round(metrics.get('metrics/recall(B)', 0.0), 4)
        
        # 计算推理速度（这里使用一个估算值，实际应该通过推理测试获得）
        model_size_mb = model_info['model_size_mb']
        if model_size_mb > 0:
            # 简单的推理速度估算（FPS），实际应该通过真实推理测试
            estimated_fps = max(10, 100 - model_size_mb * 2)  # 模型越大，FPS越低
            model_info['inference_speed'] = round(estimated_fps, 2)
        
        logger.info(f"获取模型信息成功: {model_info}")
        
    except Exception as e:
        logger.error(f"获取模型信息失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
    
    return model_info

def save_models_info_to_json(models_info, task_name):
    """
    保存多个模型信息到 training_module.json 文件
    Args:
        models_info: 模型信息字典列表
        task_name: 任务名称
    """
    try:
        # 构建文件路径 - 使用当前目录而非固定的/workspace路径
        current_dir = os.getcwd()
        json_file_path = os.path.join(current_dir, "training_module.json")
        logger.info(f"准备保存 {len(models_info)} 个模型信息到: {json_file_path}")
        
        # 读取现有数据（如果存在）
        existing_data = []
        if os.path.exists(json_file_path):
            try:
                with open(json_file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if content.strip():  # 确保文件不为空
                        existing_data = json.loads(content)
                    logger.info(f"成功读取现有数据，包含 {len(existing_data)} 条记录")
            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析错误，创建新文件: {e}")
                existing_data = []
            except FileNotFoundError:
                logger.warning(f"文件不存在，将创建新文件")
                existing_data = []
            except Exception as e:
                logger.error(f"读取文件时出错: {e}")
                existing_data = []
        
        # 为每个模型更新或添加记录
        updated_models = set()
        for model_info in models_info:
            model_name = model_info['model_name']
            updated = False
            
            # 检查是否已存在相同模型名称的记录
            for i, item in enumerate(existing_data):
                if item.get('model_name') == model_name:
                    # 更新现有记录
                    existing_data[i] = model_info
                    updated = True
                    logger.info(f"更新现有模型记录: {model_name}")
                    break
            
            if not updated:
                # 添加新记录
                existing_data.append(model_info)
                logger.info(f"添加新模型记录: {model_name}")
            
            updated_models.add(model_name)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(json_file_path) or '.', exist_ok=True)
        
        # 保存到文件 - 使用临时文件确保写入安全
        temp_file = json_file_path + '.tmp'
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, indent=2, ensure_ascii=False)
            f.flush()
            os.fsync(f.fileno())  # 确保数据写入磁盘
        
        # 安全地替换原文件
        if os.path.exists(temp_file):
            if os.path.exists(json_file_path):
                os.replace(temp_file, json_file_path)  # 原子操作替换文件
            else:
                os.rename(temp_file, json_file_path)
            
            logger.info(f"模型信息已成功保存到 {json_file_path}，更新了 {len(updated_models)} 个模型")
        else:
            logger.error(f"临时文件未创建成功: {temp_file}")
        
    except Exception as e:
        logger.error(f"保存模型信息到JSON失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

def save_model_info_to_json(model_info, task_name):
    """
    保存单个模型信息到 training_module.json 文件（为兼容性保留）
    """
    save_models_info_to_json([model_info], task_name)

def check_existing_model_info(model_name):
    """
    检查模型是否已存在于training_module.json中
    Args:
        model_name: 模型文件名
    Returns:
        bool: 如果存在返回True，否则返回False
    """
    try:
        # 构建文件路径 - 使用当前目录而非固定的/workspace路径
        current_dir = os.getcwd()
        json_file_path = os.path.join(current_dir, "training_module.json")
        
        # 如果文件不存在，返回False
        if not os.path.exists(json_file_path):
            return False
        
        # 读取现有数据
        with open(json_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if not content.strip():  # 文件为空
                return False
                
            existing_data = json.loads(content)
            
            # 检查是否存在相同名称的模型
            for item in existing_data:
                if item.get('model_name') == model_name:
                    return True
            
            return False
            
    except Exception as e:
        logger.error(f"检查模型是否存在时出错: {e}")
        return False  # 出错时默认返回False，允许保存

def setup_environment(mount_path=None):
    """设置Python环境和路径"""
    # 设置默认路径
    if mount_path is None:
        mount_path = '/root/siton-data-c16a16a2cdc44452a1c4267121b485aa'
    ultralytics_dir = os.path.join(mount_path, 'data', 'ultralytics_v8')

    # 添加到Python路径（避免重复添加）
    ultralytics_str = str(ultralytics_dir)
    if ultralytics_str not in sys.path:
        sys.path.insert(0, ultralytics_str)
        print(f"✅ 已添加ultralytics路径到sys.path: {ultralytics_str}")

    # 设置环境变量
    current_pythonpath = os.environ.get("PYTHONPATH", "")
    if ultralytics_str not in current_pythonpath:
        if current_pythonpath:
            os.environ["PYTHONPATH"] = f"{ultralytics_str}:{current_pythonpath}"
        else:
            os.environ["PYTHONPATH"] = ultralytics_str
        print(f"✅ 已设置PYTHONPATH环境变量")

    return ultralytics_dir

def main():
    """主函数"""
    print("🚀 华为NPU单机多卡YOLO训练")
    print(f"📁 工作目录: {Path(__file__).parent.absolute()}")

    # 先解析参数
    args = parse_arguments()

    # 根据参数设置环境
    ultralytics_dir = setup_environment(
        mount_path=getattr(args, 'mount_path', None)
    )

    # 现在可以安全导入ultralytics
    try:
        from ultralytics import YOLO
        print(f"\n✅ 使用ultralytics版本: {YOLO.__module__}")
        print(f"✅ ultralytics路径: {ultralytics_dir}")
    except ImportError as e:
        print(f"\n❌ ultralytics导入失败: {e}")
        print(f"请确保ultralytics_v8目录存在: {ultralytics_dir}")
        sys.exit(1)

    # 读取配置文件
    try:
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"✅ 成功加载配置文件: {args.config}")
    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {args.config}")
        print("💡 请确保配置文件存在，或使用 --config 参数指定正确路径")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件格式错误: {e}")
        sys.exit(1)
    
    try:
        
        # 检查NPU环境
        print("\n" + "="*50)
        print("🔧 检查NPU环境")
        print("="*50)
        setup_npu_environment()
        
        # 配置设备
        print("\n" + "="*50)
        print("⚙️ 配置训练设备")
        print("="*50)
        device = detect_device()  # 自动检测设备

        # 解析训练配置
        train_args = parse_training_config(config, args.data, device)

        # 命令行参数覆盖配置文件参数
        if args.model:  
            model_path = args.model
        else:
            model_path = config.get('model', {}).get('path', 'yolov8n.pt')

        # 如果提供了task_id，更新name
        if args.task_id:
            train_args['name'] = f'task_{args.task_id}'

        print(f"📋 训练配置:")
        print(f"   模型: {model_path}")
        print(f"   数据集: {train_args['data']}")
        print(f"   轮次: {train_args['epochs']}")
        print(f"   批次大小: {train_args['batch']}")
        print(f"   设备: {device}")

        # 启动单机训练
        results = single_machine_train(train_args, model_path, device)

        print("✅ 所有训练任务完成！")
        
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
