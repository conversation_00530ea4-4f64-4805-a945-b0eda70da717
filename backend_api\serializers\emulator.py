from rest_framework import serializers
from backend_api.models.emulator import Emulator

class EmulatorSerializer(serializers.ModelSerializer):
    creater_name = serializers.SerializerMethodField()
    base_image = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    engine_type = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    engine_size = serializers.IntegerField(required=False, allow_null=True)
    need_render = serializers.BooleanField(required=False, default=False)
    desc = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    address = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    
    def get_creater_name(self, emulator):
        if emulator.creater:
            return emulator.creater.username
        return ""
        
    class Meta:
        model = Emulator
        fields = [
            'id', 'emulator_id', 'name', 'desc', 'address',
            'create_time', 'base_image', 'engine_type',
            'engine_size', 'need_render', 'creater',
            'creater_name'
        ]
        read_only_fields = ['emulator_id', 'create_time']
