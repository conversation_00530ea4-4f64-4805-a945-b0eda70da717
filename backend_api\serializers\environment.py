from rest_framework import serializers
from backend_api.models.environment import Environment
import re


class EnvironmentSerializer(serializers.ModelSerializer):
    """环境基础序列化器"""
    
    class Meta:
        model = Environment
        fields = [
            'id', 'env_id', 'name', 'version', 'status',
            'harbor_url', 'gitlab_url', 'env_variables',
            'create_time', 'update_time', 'deprecated_time',
            'artifact_name'
        ]
        read_only_fields = ['env_id', 'create_time', 'update_time', 'deprecated_time']


class EnvironmentDetailSerializer(EnvironmentSerializer):
    """环境详情序列化器，包含更多字段和关联信息"""
    
    creator_name = serializers.CharField(source='creator.username', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta(EnvironmentSerializer.Meta):
        fields = EnvironmentSerializer.Meta.fields + [
            'description', 'creator', 'creator_name',
            'status_display', 'image_file'
        ]
        read_only_fields = EnvironmentSerializer.Meta.read_only_fields + ['creator']


class EnvironmentCreateSerializer(serializers.ModelSerializer):
    """环境创建序列化器，用于创建新环境"""
    
    class Meta:
        model = Environment
        fields = [
            'name', 'version', 'description', 'harbor_url',
            'gitlab_url', 'env_variables', 'creator', 'image_file',
            'artifact_name'
        ]
        read_only_fields = ['creator']

    def validate_artifact_name(self, value):
        """验证镜像名称"""
        if not value or not value.strip():
            raise serializers.ValidationError("镜像名称不能为空")
            
        # 检查是否只包含允许的字符（小写字母、数字、横杠、下划线和点）
        if not re.match(r'^[a-z0-9][a-z0-9_.-]*[a-z0-9]$', value):
            raise serializers.ValidationError(
                "镜像名称只能包含小写字母、数字、下划线、中划线和点，且必须以字母或数字开头和结尾"
            )
        
        # 检查是否包含连续的点或横杠
        if '..' in value or '--' in value:
            raise serializers.ValidationError("镜像名称不能包含连续的点或横杠")
        
        # 检查每个部分的长度（以点分隔）
        parts = value.split('.')
        for part in parts:
            if len(part) > 63:
                raise serializers.ValidationError("镜像名称的每个部分（以点分隔）长度不能超过63个字符")
        
        # 检查总长度
        if len(value) > 255:
            raise serializers.ValidationError("镜像名称总长度不能超过255个字符")
            
        return value.lower()  # 确保返回小写形式

    def validate(self, attrs):
        """验证数据"""
        # 验证环境名称和版本的唯一性
        name = attrs.get('name')
        version = attrs.get('version')
        if Environment.objects.filter(name=name, version=version).exists():
            raise serializers.ValidationError({
                "name": f"环境 {name} 版本 {version} 已存在"
            })
        
        # 验证环境变量格式
        env_variables = attrs.get('env_variables')
        if env_variables is not None and not isinstance(env_variables, dict):
            raise serializers.ValidationError({
                "env_variables": "环境变量必须是JSON对象格式"
            })
            
        return attrs

    def create(self, validated_data):
        """创建环境"""
        # 设置初始状态为可用
        validated_data['status'] = Environment.StatusChoice.ACTIVE
        return super().create(validated_data)
        


