from rest_framework.response import Response
from rest_framework.parsers import MultiPartParser
from rest_framework.viewsets import ModelViewSet
from rest_framework.decorators import action
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_204_NO_CONTENT, HTTP_400_BAD_REQUEST
from django_filters.rest_framework import DjangoFilterBackend

from backend_api.models.policy.policy import Policy
from backend_api.models.evaluation.evaluation import Evaluation
from backend_api.models.policy.policy_evaluation import PolicyEvaluation
from backend_api.models.evaluation.evaluation_test import EvaluationTest
from backend_api.serializers.policy.policy import PolicySerializer, PolicyDetailSerializer


class PolicyViewSets(ModelViewSet):
    """
    策略视图集，提供策略管理的API接口
    包括：获取策略信息、创建策略、更新策略、删除策略、关联评估、运行入库测试等
    """
    queryset = Policy.objects.all()
    serializer_class = PolicySerializer
    parser_classes = [MultiPartParser]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['status', 'policy_type']
    
    def get_serializer_class(self):
        """根据操作类型选择不同的序列化器"""
        if self.action == 'list' or self.action == 'retrieve':
            return PolicyDetailSerializer
        return PolicySerializer
    
    def get_queryset(self):
        """获取查询集，可根据用户权限过滤"""
        user = self.request.user
        queryset = self.queryset
        
        # 根据名称过滤
        name = self.request.query_params.get('name', '')
        if name:
            queryset = queryset.filter(name__icontains=name)
        
        # 根据评估ID过滤
        evaluation_id = self.request.query_params.get('evaluation_id', '')
        if evaluation_id:
            queryset = queryset.filter(evaluations__evaluation_id=evaluation_id)
            
        # 如果不是管理员，只能看到自己创建的策略
        if not user.is_staff:
            queryset = queryset.filter(creater=user.id)
            
        return queryset
    
    def create(self, request):
        """创建策略"""
        user = request.user
        request.data["creater"] = user.id
        
        # 检查策略名称是否已存在
        name = request.data.get("name")
        if Policy.objects.filter(name=name).exists():
            return Response(
                {"err_msg": f"策略 {name} 已存在", "msg": "对象已存在", "code": "object_exists"}, 
                status=HTTP_400_BAD_REQUEST
            )
        
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            policy = serializer.save()
            return Response(
                {"data": PolicyDetailSerializer(policy).data, "msg": "策略创建成功", "code": 200}, 
                status=HTTP_201_CREATED
            )
        else:
            return Response(
                {"err_msg": serializer.errors, "msg": "参数错误", "code": 400}, 
                status=HTTP_400_BAD_REQUEST
            )
    
    def destroy(self, request, *args, **kwargs):
        """删除策略"""
        instance = self.get_object()
        self.perform_destroy(instance)
        return Response({"msg": "策略删除成功", "code": 200}, status=HTTP_204_NO_CONTENT)
    
    def retrieve(self, request, *args, **kwargs):
        """获取单个策略详情"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({"data": serializer.data, "code": 200}, status=HTTP_200_OK)
    
    def list(self, request, *args, **kwargs):
        """获取策略列表"""
        queryset = self.filter_queryset(self.get_queryset())
        
        # 分页
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({"data": serializer.data, "code": 200}, status=HTTP_200_OK)
    
    @action(detail=True, methods=['post'])
    def associate_evaluation(self, request, pk=None):
        """关联策略与评估"""
        policy = self.get_object()
        evaluation_id = request.data.get('evaluation_id')
        
        if not evaluation_id:
            return Response(
                {"err_msg": "缺少评估ID", "msg": "参数错误", "code": 400}, 
                status=HTTP_400_BAD_REQUEST
            )
        
        try:
            evaluation = Evaluation.objects.get(evaluation_id=evaluation_id)
        except Evaluation.DoesNotExist:
            return Response(
                {"err_msg": "评估不存在", "msg": "对象不存在", "code": "not_found"}, 
                status=HTTP_400_BAD_REQUEST
            )
        
        # 检查是否已经关联
        if PolicyEvaluation.objects.filter(policy=policy, evaluation=evaluation).exists():
            return Response(
                {"err_msg": "该策略已与此评估关联", "msg": "关联已存在", "code": "already_associated"}, 
                status=HTTP_400_BAD_REQUEST
            )
        
        # 创建关联
        policy_evaluation = PolicyEvaluation.objects.create(
            policy=policy,
            evaluation=evaluation
        )
        
        # 更新策略状态为待入库测试
        policy.status = Policy.StatusChoice.PENDING
        policy.save()
        
        return Response(
            {"msg": "策略已与评估关联", "code": 200},
            status=HTTP_200_OK
        )
    
    @action(detail=True, methods=['post'])
    def run_tests(self, request, pk=None):
        """运行策略的入库测试"""
        policy = self.get_object()
        evaluation_id = request.data.get('evaluation_id')
        
        if not evaluation_id:
            return Response(
                {"err_msg": "缺少评估ID", "msg": "参数错误", "code": 400}, 
                status=HTTP_400_BAD_REQUEST
            )
        
        try:
            evaluation = Evaluation.objects.get(evaluation_id=evaluation_id)
        except Evaluation.DoesNotExist:
            return Response(
                {"err_msg": "评估不存在", "msg": "对象不存在", "code": "not_found"}, 
                status=HTTP_400_BAD_REQUEST
            )
        
        # 检查是否已经关联
        try:
            policy_evaluation = PolicyEvaluation.objects.get(policy=policy, evaluation=evaluation)
        except PolicyEvaluation.DoesNotExist:
            return Response(
                {"err_msg": "该策略未与此评估关联", "msg": "关联不存在", "code": "not_associated"}, 
                status=HTTP_400_BAD_REQUEST
            )
        
        # 获取评估的所有测试
        tests = EvaluationTest.objects.filter(evaluation=evaluation)
        if not tests.exists():
            return Response(
                {"err_msg": "该评估没有可运行的测试", "msg": "测试不存在", "code": "no_tests"}, 
                status=HTTP_400_BAD_REQUEST
            )
        
        # 更新策略和策略评估状态
        policy.status = Policy.StatusChoice.TESTING
        policy.save()
        
        policy_evaluation.status = PolicyEvaluation.StatusChoice.RUNNING
        policy_evaluation.save()
        
        # TODO: 实际测试运行逻辑，可以在这里调用异步任务
        # 此处应该调用Celery任务来执行评估测试
        
        return Response(
            {"msg": "入库测试已开始运行", "code": 200},
            status=HTTP_200_OK
        ) 